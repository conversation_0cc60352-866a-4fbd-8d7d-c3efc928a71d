<?php

namespace App\Notifications;

use App\Models\OrganizationRequest;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OrganizationRequestApproved extends Notification implements ShouldBroadcast
{

    public $organizationRequest;

    /**
     * Create a new notification instance.
     */
    public function __construct(OrganizationRequest $organizationRequest)
    {
        $this->organizationRequest = $organizationRequest;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        $channels = [];

        // Check if user wants to receive organization approval notifications
        if ($notifiable->wantsNotification('organization_approvals')) {
            $channels[] = 'database';
            $channels[] = 'broadcast';
        }

        return $channels;
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'organization_request_approved',
            'user_id' => $this->organizationRequest->reviewer->id,
            'user_name' => $this->organizationRequest->reviewer->name,
            'user_avatar' => $this->organizationRequest->reviewer->getNotificationAvatarUrl(),
            'organization_name' => $this->organizationRequest->organization_name,
            'reviewer_name' => $this->organizationRequest->reviewer->name,
            'message' => $this->getMessage(),
            'url' => route('organization-requests.show', $this->organizationRequest),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'organization_request_approved',
            'user_id' => $this->organizationRequest->reviewer->id,
            'user_name' => $this->organizationRequest->reviewer->name,
            'user_avatar' => $this->organizationRequest->reviewer->getNotificationAvatarUrl(),
            'organization_name' => $this->organizationRequest->organization_name,
            'request_id' => $this->organizationRequest->id,
            'reviewer_name' => $this->organizationRequest->reviewer->name,
            'message' => $this->getMessage(),
            'url' => route('organization-requests.show', $this->organizationRequest),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "Your request to create the organization '{$this->organizationRequest->organization_name}' has been approved! You can now create your organization.";
    }
}
