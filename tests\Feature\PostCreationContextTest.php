<?php

namespace Tests\Feature;

use App\Models\Organization;
use App\Models\Group;
use App\Models\Post;
use App\Models\PostMethod;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PostCreationContextTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed the post methods and tags
        $this->artisan('db:seed', ['--class' => 'PostMethodSeeder']);
    }

    /** @test */
    public function personal_posts_show_user_tags()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $personalTag = Tag::where('name', 'Personal')->first();

        $response = $this->actingAs($user)->post('/posts', [
            'title' => 'Personal Post',
            'content' => 'This is a personal post.',
            'tags' => [$personalTag->id],
            'status' => 'published'
        ]);

        $response->assertRedirect();
        
        $post = Post::where('title', 'Personal Post')->first();
        $this->assertNotNull($post);
        $this->assertEquals($userMethod->id, $post->post_method_id);
        $this->assertNull($post->organization_id);
        $this->assertNull($post->group_id);
        $this->assertTrue($post->tags->contains($personalTag));
    }

    /** @test */
    public function organization_posts_show_organization_tags()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->create();
        
        // Add user as officer
        $organization->members()->attach($user->id, [
            'role' => 'officer',
            'status' => 'active',
            'joined_at' => now()
        ]);

        $orgMethod = PostMethod::where('slug', 'organization')->first();
        $announcementTag = Tag::where('name', 'Announcement')->first();

        $response = $this->actingAs($user)->post('/posts', [
            'title' => 'Organization Announcement',
            'content' => 'This is an organization announcement.',
            'organization_id' => $organization->id,
            'tags' => [$announcementTag->id],
            'status' => 'published'
        ]);

        $response->assertRedirect();
        
        $post = Post::where('title', 'Organization Announcement')->first();
        $this->assertNotNull($post);
        $this->assertEquals($orgMethod->id, $post->post_method_id);
        $this->assertEquals($organization->id, $post->organization_id);
        $this->assertNull($post->group_id);
        $this->assertTrue($post->tags->contains($announcementTag));
    }

    /** @test */
    public function group_posts_show_group_tags()
    {
        $user = User::factory()->create();
        $group = Group::factory()->create();
        
        // Add user as member
        $group->members()->attach($user->id, [
            'role' => 'member',
            'status' => 'active',
            'joined_at' => now()
        ]);

        $groupMethod = PostMethod::where('slug', 'group')->first();
        $discussionTag = Tag::where('name', 'Discussion')->first();

        $response = $this->actingAs($user)->post('/posts', [
            'title' => 'Group Discussion',
            'content' => 'This is a group discussion.',
            'group_id' => $group->id,
            'tags' => [$discussionTag->id],
            'status' => 'published'
        ]);

        $response->assertRedirect();
        
        $post = Post::where('title', 'Group Discussion')->first();
        $this->assertNotNull($post);
        $this->assertEquals($groupMethod->id, $post->post_method_id);
        $this->assertEquals($group->id, $post->group_id);
        $this->assertNull($post->organization_id);
        $this->assertTrue($post->tags->contains($discussionTag));
    }

    /** @test */
    public function only_organization_officers_can_create_organization_posts()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->create();
        
        // User is not a member - should fail
        $response = $this->actingAs($user)->post('/posts', [
            'title' => 'Unauthorized Post',
            'content' => 'This should fail.',
            'organization_id' => $organization->id,
            'status' => 'published'
        ]);

        $response->assertSessionHasErrors(['organization_id']);

        // Add user as regular member - should still fail
        $organization->members()->attach($user->id, [
            'role' => 'member',
            'status' => 'active',
            'joined_at' => now()
        ]);

        $response = $this->actingAs($user)->post('/posts', [
            'title' => 'Member Post',
            'content' => 'This should also fail.',
            'organization_id' => $organization->id,
            'status' => 'published'
        ]);

        $response->assertSessionHasErrors(['organization_id']);

        // Update user to officer - should succeed
        $organization->members()->updateExistingPivot($user->id, ['role' => 'officer']);

        $response = $this->actingAs($user)->post('/posts', [
            'title' => 'Officer Post',
            'content' => 'This should succeed.',
            'organization_id' => $organization->id,
            'status' => 'published'
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('posts', ['title' => 'Officer Post']);
    }

    /** @test */
    public function dashboard_modal_shows_correct_tags_based_on_organization_selection()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->create();
        
        // Add user as officer
        $organization->members()->attach($user->id, [
            'role' => 'officer',
            'status' => 'active',
            'joined_at' => now()
        ]);

        // Test dashboard page loads
        $response = $this->actingAs($user)->get('/dashboard');
        $response->assertStatus(200);
        
        // Test that organization is available in the dropdown
        $response->assertSee($organization->name);
    }
}
