<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Post;
use App\Models\Tag;
use App\Models\PostMethod;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Carbon\Carbon;

class ImprovedFilterTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $userPostMethod;
    protected $orgPostMethod;
    protected $groupPostMethod;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        // Create post methods
        $this->userPostMethod = PostMethod::firstOrCreate([
            'slug' => 'user'
        ], [
            'name' => 'User',
            'description' => 'Posts created by individual users',
            'is_active' => true
        ]);

        $this->orgPostMethod = PostMethod::firstOrCreate([
            'slug' => 'organization'
        ], [
            'name' => 'Organization',
            'description' => 'Posts created by organizations',
            'is_active' => true
        ]);

        $this->groupPostMethod = PostMethod::firstOrCreate([
            'slug' => 'group'
        ], [
            'name' => 'Group',
            'description' => 'Posts created within groups',
            'is_active' => true
        ]);
    }

    public function test_date_filter_today_works()
    {
        // Create posts with different dates
        $todayPost = Post::factory()->create([
            'title' => 'Today Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $yesterdayPost = Post::factory()->create([
            'title' => 'Yesterday Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()->subDay()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?date_range=today');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $html = $response->json('html');
        $this->assertStringContainsString('Today Post', $html);
        $this->assertStringNotContainsString('Yesterday Post', $html);
    }

    public function test_date_filter_week_works()
    {
        // Create posts with different dates
        $thisWeekPost = Post::factory()->create([
            'title' => 'This Week Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()->startOfWeek()->addDay()
        ]);

        $lastWeekPost = Post::factory()->create([
            'title' => 'Last Week Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()->subWeek()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?date_range=week');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $html = $response->json('html');
        $this->assertStringContainsString('This Week Post', $html);
        $this->assertStringNotContainsString('Last Week Post', $html);
    }

    public function test_date_filter_month_works()
    {
        // Create posts with different dates
        $thisMonthPost = Post::factory()->create([
            'title' => 'This Month Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()->startOfMonth()->addDay()
        ]);

        $lastMonthPost = Post::factory()->create([
            'title' => 'Last Month Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()->subMonth()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?date_range=month');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $html = $response->json('html');
        $this->assertStringContainsString('This Month Post', $html);
        $this->assertStringNotContainsString('Last Month Post', $html);
    }

    public function test_privacy_filter_logic_for_user_posts()
    {
        // Create user post
        $userPost = Post::factory()->create([
            'title' => 'User Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'organization_id' => null,
            'group_id' => null,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Test public filter for user posts
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?post_type=user&privacy=public');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('User Post', $response->json('html'));
    }

    public function test_privacy_filter_logic_for_org_posts()
    {
        $organization = Organization::factory()->create();
        
        // Create organization post
        $orgPost = Post::factory()->create([
            'title' => 'Organization Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->orgPostMethod->id,
            'organization_id' => $organization->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Test org members filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?post_type=organization&privacy=org_members');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Organization Post', $response->json('html'));
    }

    public function test_combined_filters_work_correctly()
    {
        $organization = Organization::factory()->create();
        
        $tag = Tag::firstOrCreate([
            'name' => 'Event',
            'post_method_id' => $this->orgPostMethod->id
        ], [
            'slug' => 'event',
            'color' => '#10B981',
            'is_active' => true
        ]);

        // Create organization post with tag from today
        $post = Post::factory()->create([
            'title' => 'Today Org Event',
            'user_id' => $this->user->id,
            'post_method_id' => $this->orgPostMethod->id,
            'organization_id' => $organization->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $post->tags()->attach($tag->id);

        // Test multiple filters: org posts + today + event tag + org privacy
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?post_type=organization&date_range=today&tags[]=' . $tag->id . '&privacy=org_members');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Today Org Event', $response->json('html'));
    }

    public function test_search_with_date_filter_combination()
    {
        // Create posts with different dates and content
        $todayPost = Post::factory()->create([
            'title' => 'Laravel Tutorial Today',
            'content' => 'Learn Laravel framework',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $oldPost = Post::factory()->create([
            'title' => 'Laravel Tutorial Old',
            'content' => 'Learn Laravel framework',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()->subWeek()
        ]);

        // Search for Laravel posts from today only
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?search=Laravel&date_range=today');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $html = $response->json('html');
        $this->assertStringContainsString('Laravel Tutorial Today', $html);
        $this->assertStringNotContainsString('Laravel Tutorial Old', $html);
    }

    public function test_no_group_filter_in_response()
    {
        // Test that group_id parameter is not processed
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?group_id=123');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        // Should return all posts since group filter is ignored
        $this->assertNotNull($response->json('html'));
    }

    public function test_empty_filters_return_no_results()
    {
        // Create a post
        Post::factory()->create([
            'title' => 'Test Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Test with no active filters (should not apply any filtering)
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        // Should return the post since no filters are active
        $this->assertStringContainsString('Test Post', $response->json('html'));
    }
}
