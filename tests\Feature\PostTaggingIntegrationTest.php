<?php

namespace Tests\Feature;

use App\Models\Post;
use App\Models\PostMethod;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PostTaggingIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed the post methods and tags
        $this->artisan('db:seed', ['--class' => 'PostMethodSeeder']);
    }

    /** @test */
    public function it_can_create_and_edit_posts_with_tags_through_web_interface()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $personalTag = Tag::where('name', 'Personal')->first();
        $reflectionTag = Tag::where('name', 'Reflection')->first();

        // Test creating a post with tags (post method determined automatically)
        $response = $this->actingAs($user)->post('/posts', [
            'title' => 'My Personal Reflection',
            'content' => 'This is a personal reflection about my journey.',
            'tags' => [$personalTag->id, $reflectionTag->id],
            'status' => 'published'
        ]);

        $response->assertRedirect();
        
        $post = Post::where('title', 'My Personal Reflection')->first();
        $this->assertNotNull($post);
        $this->assertEquals(2, $post->tags()->count());

        // Test editing the post to change tags
        $celebrationTag = Tag::where('name', 'Celebration')->first();
        
        $response = $this->actingAs($user)->put("/posts/{$post->id}", [
            'title' => 'My Personal Reflection - Updated',
            'content' => 'This is an updated personal reflection about my journey.',
            'tags' => [$personalTag->id, $celebrationTag->id], // Changed reflection to celebration
            'status' => 'published'
        ]);

        $response->assertRedirect();
        
        $post->refresh();
        $this->assertEquals('My Personal Reflection - Updated', $post->title);
        $this->assertEquals(2, $post->tags()->count());
        $this->assertTrue($post->tags->contains($personalTag));
        $this->assertTrue($post->tags->contains($celebrationTag));
        $this->assertFalse($post->tags->contains($reflectionTag));
    }

    /** @test */
    public function it_displays_posts_with_tags_correctly_in_dashboard()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $personalTag = Tag::where('name', 'Personal')->first();

        $post = Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $userMethod->id,
            'title' => 'Tagged Post for Dashboard',
            'content' => 'This post should display with tags.',
            'status' => 'published',
            'published_at' => now()
        ]);
        $post->tags()->attach($personalTag->id);

        $response = $this->actingAs($user)->get('/dashboard');
        
        $response->assertStatus(200);
        $response->assertSee('Tagged Post for Dashboard');
        $response->assertSee($personalTag->name);
        $response->assertSee($userMethod->name);
    }

    /** @test */
    public function it_can_filter_posts_by_multiple_criteria()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $orgMethod = PostMethod::where('slug', 'organization')->first();
        $personalTag = Tag::where('name', 'Personal')->first();
        $announcementTag = Tag::where('name', 'Announcement')->first();

        // Create posts with different combinations
        $userPost = Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $userMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);
        $userPost->tags()->attach($personalTag->id);

        $orgPost = Post::factory()->create([
            'user_id' => $user->id,
            'post_method_id' => $orgMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);
        $orgPost->tags()->attach($announcementTag->id);

        // Test filtering by post method
        $response = $this->actingAs($user)->get("/posts-filter?post_method_id={$userMethod->id}");
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(1, $data['count']);

        // Test filtering by tag
        $response = $this->actingAs($user)->get("/posts-filter?tags[]={$personalTag->id}");
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(1, $data['count']);

        // Test combined filtering (should return no results as no post has both criteria)
        $response = $this->actingAs($user)->get("/posts-filter?post_method_id={$orgMethod->id}&tags[]={$personalTag->id}");
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertEquals(0, $data['count']);
    }

    /** @test */
    public function api_endpoint_returns_correct_tags_for_post_method()
    {
        $user = User::factory()->create();
        $userMethod = PostMethod::where('slug', 'user')->first();
        $orgMethod = PostMethod::where('slug', 'organization')->first();

        // Test user method tags
        $response = $this->actingAs($user)->get("/api/post-methods/tags?post_method_id={$userMethod->id}");
        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertArrayHasKey('tags', $data);
        $tagNames = collect($data['tags'])->pluck('name')->toArray();
        $this->assertContains('Personal', $tagNames);
        $this->assertContains('Reflection', $tagNames);
        $this->assertNotContains('Announcement', $tagNames); // This belongs to organization method

        // Test organization method tags
        $response = $this->actingAs($user)->get("/api/post-methods/tags?post_method_id={$orgMethod->id}");
        $response->assertStatus(200);
        $data = $response->json();
        
        $tagNames = collect($data['tags'])->pluck('name')->toArray();
        $this->assertContains('Announcement', $tagNames);
        $this->assertContains('Event', $tagNames);
        $this->assertNotContains('Personal', $tagNames); // This belongs to user method
    }
}
