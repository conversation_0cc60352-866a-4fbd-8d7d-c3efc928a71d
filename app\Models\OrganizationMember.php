<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

class OrganizationMember extends Pivot
{
    /**
     * The table associated with the model.
     */
    protected $table = 'organization_members';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'role',
        'custom_role_title',
        'status',
        'joined_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'joined_at' => 'datetime',
    ];

    /**
     * Get the display name for the role
     */
    public function getRoleDisplayName(): string
    {
        if ($this->custom_role_title) {
            return $this->custom_role_title;
        }

        return match($this->role) {
            'president' => 'President',
            'vice_president' => 'Vice President',
            'secretary' => 'Secretary',
            'treasurer' => 'Treasurer',
            'officer' => 'Officer',
            'member' => 'Member',
            default => ucfirst(str_replace('_', ' ', $this->role)),
        };
    }

    /**
     * Check if this member has officer-level privileges
     */
    public function isOfficer(): bool
    {
        return in_array($this->role, ['president', 'vice_president', 'secretary', 'treasurer', 'officer']);
    }

    /**
     * Check if this member is the president
     */
    public function isPresident(): bool
    {
        return $this->role === 'president';
    }

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = true;
}
