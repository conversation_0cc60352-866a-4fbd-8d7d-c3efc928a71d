<x-layouts.unilink-layout>
    <!-- Organization Page Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Image -->
        <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
            @if($organization->cover_image)
                <img src="{{ Storage::disk('public')->url($organization->cover_image) }}" alt="{{ $organization->name }}" class="w-full h-full object-cover">
            @endif
            
            <!-- Action Buttons -->
            <div class="absolute top-4 right-4 flex space-x-2">
                <livewire:organization-follower :organization="$organization" />

                @if($organization->userCanPost(auth()->user()))
                    <button onclick="openGroupPostModal()" class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700">
                        Create Post
                    </button>
                @endif

                @if($userMembership && in_array($userMembership->pivot->role, ['officer', 'president']) || auth()->user()->isAdmin())
                    <a href="{{ route('organizations.edit', $organization) }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        Manage
                    </a>
                @endif
            </div>
        </div>

        <!-- Organization Info -->
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white rounded-lg shadow-md flex items-center justify-center -mt-10 border-4 border-white">
                        @if($organization->logo)
                            <img src="{{ Storage::disk('public')->url($organization->logo) }}" alt="{{ $organization->name }}" class="w-16 h-16 rounded-lg object-cover">
                        @else
                            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-blue-600 font-bold text-lg">{{ substr($organization->name, 0, 2) }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Organization Details -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h1 class="text-2xl font-bold text-gray-900">{{ $organization->name }}</h1>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Official Page
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $organization->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ ucfirst($organization->status) }}
                            </span>
                        </div>
                    </div>
                    
                    <p class="text-gray-600 mt-2">{{ $organization->description }}</p>
                    
                    @if($organization->about)
                        <div class="mt-3">
                            <h3 class="text-sm font-medium text-gray-900">About</h3>
                            <p class="text-gray-600 text-sm mt-1">{{ $organization->about }}</p>
                        </div>
                    @endif
                    
                    <!-- Stats -->
                    <div class="flex items-center space-x-6 mt-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                            {{ $followersCount }} followers
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {{ $organization->officers->count() }} officers
                        </div>
                        @if($organization->founded_date)
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                                </svg>
                                Founded {{ $organization->founded_date->format('M Y') }}
                            </div>
                        @endif
                    </div>

                    <!-- Contact Info -->
                    @if($organization->email || $organization->phone || $organization->website)
                        <div class="flex items-center space-x-4 mt-4">
                            @if($organization->email)
                                <a href="mailto:{{ $organization->email }}" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    {{ $organization->email }}
                                </a>
                            @endif
                            @if($organization->phone)
                                <a href="tel:{{ $organization->phone }}" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                    {{ $organization->phone }}
                                </a>
                            @endif
                            @if($organization->website)
                                <a href="{{ $organization->website }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                    Website
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Announcements & Posts -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Latest Announcements</h2>
                </div>
                
                @if($organization->posts->count() > 0)
                    <div class="space-y-6">
                        @foreach($organization->posts as $post)
                            <x-post-card :post="$post" />
                        @endforeach
                    </div>
                @else
                    <div class="p-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No announcements yet</h3>
                        <p class="mt-1 text-sm text-gray-500">This organization hasn't posted any announcements yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Officers -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Officers</h3>
                </div>
                
                <div class="p-4">
                    <div class="space-y-3">
                        @foreach($organization->officers->take(5) as $officer)
                            <div class="flex items-center space-x-3">
                                <img class="h-8 w-8 rounded-full" src="{{ $officer->avatar ? Storage::disk('public')->url($officer->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($officer->name) . '&color=7F9CF5&background=EBF4FF' }}" alt="{{ $officer->name }}">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $officer->name }}</p>
                                    <p class="text-xs text-gray-500">{{ ucfirst($officer->pivot->role) }}</p>
                                </div>
                                @if($officer->pivot->role === 'president')
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                        President
                                    </span>
                                @elseif($officer->pivot->role === 'officer')
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Officer
                                    </span>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Related Groups -->
            @if($organization->groups->count() > 0)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Related Groups</h3>
                    </div>
                    
                    <div class="p-4">
                        <div class="space-y-3">
                            @foreach($organization->groups->take(5) as $group)
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        @if($group->logo)
                                            <img src="{{ Storage::disk('public')->url($group->logo) }}" alt="{{ $group->name }}" class="w-6 h-6 rounded object-cover">
                                        @else
                                            <span class="text-blue-600 font-bold text-xs">{{ substr($group->name, 0, 2) }}</span>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <a href="{{ route('groups.show', $group) }}" class="text-sm font-medium text-gray-900 hover:text-blue-600 truncate block">
                                            {{ $group->name }}
                                        </a>
                                        <p class="text-xs text-gray-500">{{ $group->activeMembers->count() }} members</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Organization Post Creation Modal -->
    @if($organization->userCanPost(auth()->user()))
        <x-group-post-creation-modal :organization="$organization" />
    @endif
</x-layouts.unilink-layout>
