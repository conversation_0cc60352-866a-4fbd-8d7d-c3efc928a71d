<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class UserFollower extends Component
{
    public User $user;
    public $isFollowing = false;
    public $followersCount = 0;
    public $isLoading = false;
    public $compact = false;

    public function mount(User $user, $compact = false)
    {
        $this->user = $user;
        $this->compact = $compact;
        $this->loadFollowStatus();
        $this->loadFollowersCount();
    }

    public function loadFollowStatus()
    {
        if (Auth::check()) {
            $this->isFollowing = Auth::user()->isFollowingUser($this->user);
        }
    }

    public function loadFollowersCount()
    {
        $this->followersCount = $this->user->followers()->count();
    }

    public function toggleFollow()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Prevent self-following
        if (Auth::id() === $this->user->id) {
            session()->flash('error', 'You cannot follow yourself.');
            return;
        }

        $this->isLoading = true;

        try {
            $currentUser = Auth::user();

            if ($this->isFollowing) {
                // Unfollow
                $currentUser->unfollowUser($this->user);
                $this->isFollowing = false;
                $this->followersCount--;
                session()->flash('success', "You have unfollowed {$this->user->name}.");

                // Dispatch event for other components to listen
                $this->dispatch('userUnfollowed', $this->user->id);
            } else {
                // Follow
                $currentUser->followUser($this->user);
                $this->isFollowing = true;
                $this->followersCount++;
                session()->flash('success', "You are now following {$this->user->name}!");

                // Dispatch event for other components to listen
                $this->dispatch('userFollowed', $this->user->id);
            }

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while updating your follow status.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.user-follower');
    }
}
