<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="admin-card p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="<?php echo e(route('admin.organization-requests.index')); ?>" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </a>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Organization Request Review</h1>
                    <p class="text-gray-600 mt-1"><?php echo e($organizationRequest->organization_name); ?></p>
                </div>
            </div>
            
            <!-- Status Badge -->
            <div>
                <?php if($organizationRequest->isPending()): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                        </svg>
                        Pending Review
                    </span>
                <?php elseif($organizationRequest->isApproved()): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        Approved
                    </span>
                <?php else: ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                        Rejected
                    </span>
                <?php endif; ?>

                <?php if($organizationRequest->hasCreatedOrganization()): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 ml-2">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        Organization Created
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Request Details -->
            <div class="admin-card p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Request Details</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Organization Name</label>
                        <p class="text-gray-900 font-medium"><?php echo e($organizationRequest->organization_name); ?></p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-900 whitespace-pre-wrap"><?php echo e($organizationRequest->description); ?></p>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Submitted On</label>
                            <p class="text-gray-900"><?php echo e($organizationRequest->created_at->format('F j, Y \a\t g:i A')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Time Ago</label>
                            <p class="text-gray-900"><?php echo e($organizationRequest->created_at->diffForHumans()); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Information -->
            <div class="admin-card p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Requester Information</h2>
                
                <div class="flex items-start space-x-4">
                    <img src="<?php echo e($organizationRequest->user->getAvatarUrl(64)); ?>" 
                         alt="<?php echo e($organizationRequest->user->name); ?>"
                         class="w-16 h-16 rounded-full">
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900"><?php echo e($organizationRequest->user->name); ?></h3>
                        <p class="text-gray-600"><?php echo e($organizationRequest->user->email); ?></p>
                        <?php if($organizationRequest->user->student_id): ?>
                            <p class="text-sm text-gray-500">Student ID: <?php echo e($organizationRequest->user->student_id); ?></p>
                        <?php endif; ?>
                        <p class="text-sm text-gray-500 capitalize">Role: <?php echo e($organizationRequest->user->role); ?></p>
                        <p class="text-sm text-gray-500">Member since: <?php echo e($organizationRequest->user->created_at->format('F Y')); ?></p>
                    </div>
                </div>
            </div>

            <!-- Proof Document -->
            <?php if($organizationRequest->proof_document): ?>
            <div class="admin-card p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Proof Document</h2>
                
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
                    <div class="flex items-center justify-center space-x-4">
                        <div class="flex-shrink-0">
                            <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="flex-1 text-center">
                            <p class="text-sm font-medium text-gray-900">Proof of Presidency Document</p>
                            <p class="text-sm text-gray-500">Uploaded <?php echo e($organizationRequest->created_at->diffForHumans()); ?></p>
                        </div>
                        <div>
                            <a href="<?php echo e(Storage::disk('public')->url($organizationRequest->proof_document)); ?>" 
                               target="_blank"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                View Document
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Previous Admin Notes -->
            <?php if($organizationRequest->admin_notes): ?>
            <div class="admin-card p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Admin Notes</h2>
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-gray-900 whitespace-pre-wrap"><?php echo e($organizationRequest->admin_notes); ?></p>
                </div>
                <?php if($organizationRequest->reviewer): ?>
                    <p class="text-sm text-gray-500 mt-2">
                        Added by <?php echo e($organizationRequest->reviewer->name); ?> on <?php echo e($organizationRequest->reviewed_at->format('F j, Y \a\t g:i A')); ?>

                    </p>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Review Actions -->
            <?php if($organizationRequest->isPending()): ?>
            <div class="admin-card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Review Actions</h3>
                
                <!-- Approve Form -->
                <form action="<?php echo e(route('admin.organization-requests.approve', $organizationRequest)); ?>" method="POST" class="mb-4">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3">
                        <label for="approve_notes" class="block text-sm font-medium text-gray-700 mb-1">Admin Notes (Optional)</label>
                        <textarea id="approve_notes" name="admin_notes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                  placeholder="Add any notes for the user..."></textarea>
                    </div>
                    <button type="submit" 
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Approve Request
                    </button>
                </form>

                <!-- Reject Form -->
                <form action="<?php echo e(route('admin.organization-requests.reject', $organizationRequest)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="mb-3">
                        <label for="reject_notes" class="block text-sm font-medium text-gray-700 mb-1">Rejection Reason *</label>
                        <textarea id="reject_notes" name="admin_notes" rows="3" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                  placeholder="Explain why this request is being rejected..."></textarea>
                    </div>
                    <button type="submit" 
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Reject Request
                    </button>
                </form>
            </div>
            <?php endif; ?>

            <!-- Request Information -->
            <div class="admin-card p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Request Information</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Status:</span>
                        <span class="text-sm font-medium text-gray-900 capitalize"><?php echo e($organizationRequest->status); ?></span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Submitted:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($organizationRequest->created_at->format('M j, Y')); ?></span>
                    </div>

                    <?php if($organizationRequest->reviewed_at): ?>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Reviewed:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($organizationRequest->reviewed_at->format('M j, Y')); ?></span>
                    </div>
                    <?php endif; ?>

                    <?php if($organizationRequest->reviewer): ?>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Reviewed by:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($organizationRequest->reviewer->name); ?></span>
                    </div>
                    <?php endif; ?>

                    <?php if($organizationRequest->hasCreatedOrganization()): ?>
                    <div class="pt-3 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">Organization:</span>
                            <a href="<?php echo e(route('organizations.show', $organizationRequest->createdOrganization)); ?>" 
                               class="text-sm font-medium text-blue-600 hover:text-blue-500">
                                View Organization
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/admin/organization-requests/show.blade.php ENDPATH**/ ?>