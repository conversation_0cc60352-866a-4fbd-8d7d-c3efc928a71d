<?php

namespace App\Notifications;

use App\Models\OrganizationRequest;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OrganizationRequestSubmitted extends Notification implements ShouldBroadcast
{

    public $organizationRequest;

    /**
     * Create a new notification instance.
     */
    public function __construct(OrganizationRequest $organizationRequest)
    {
        $this->organizationRequest = $organizationRequest;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        $channels = [];

        // Check if user wants to receive admin notifications
        if ($notifiable->wantsNotification('admin_notifications')) {
            $channels[] = 'database';
            $channels[] = 'broadcast';
        }

        return $channels;
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'organization_request_submitted',
            'user_id' => $this->organizationRequest->user->id,
            'user_name' => $this->organizationRequest->user->name,
            'user_avatar' => $this->organizationRequest->user->getNotificationAvatarUrl(),
            'organization_name' => $this->organizationRequest->organization_name,
            'message' => $this->getMessage(),
            'url' => route('admin.organization-requests.show', $this->organizationRequest),
            'created_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'organization_request_submitted',
            'user_id' => $this->organizationRequest->user->id,
            'user_name' => $this->organizationRequest->user->name,
            'user_avatar' => $this->organizationRequest->user->getNotificationAvatarUrl(),
            'organization_name' => $this->organizationRequest->organization_name,
            'request_id' => $this->organizationRequest->id,
            'message' => $this->getMessage(),
            'url' => route('admin.organization-requests.show', $this->organizationRequest),
        ];
    }

    /**
     * Get the notification message
     */
    private function getMessage(): string
    {
        return "{$this->organizationRequest->user->name} has submitted a request to create the organization '{$this->organizationRequest->organization_name}'";
    }
}
