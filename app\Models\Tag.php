<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Tag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'post_method_id',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the post method this tag belongs to
     */
    public function postMethod(): BelongsTo
    {
        return $this->belongsTo(PostMethod::class);
    }

    /**
     * Get all posts that have this tag
     */
    public function posts(): BelongsToMany
    {
        return $this->belongsToMany(Post::class, 'post_tags');
    }

    /**
     * Scope for active tags
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for tags by post method
     */
    public function scopeByPostMethod($query, $postMethodId)
    {
        return $query->where('post_method_id', $postMethodId);
    }

    /**
     * Get tag by slug
     */
    public function scopeBySlug($query, $slug)
    {
        return $query->where('slug', $slug);
    }

    /**
     * Get the tag's color with fallback
     */
    public function getColorAttribute($value)
    {
        return $value ?: '#3B82F6';
    }
}
