<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="<?php echo e(route('posts.show', $post)); ?>" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Post</h1>
                <p class="text-gray-600 mt-1">Update your post content</p>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-3xl">
        <form action="<?php echo e(route('posts.update', $post)); ?>" method="POST" enctype="multipart/form-data" class="space-y-6">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <!-- User Info -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <img class="h-12 w-12 rounded-full" src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                    <div>
                        <p class="font-medium text-gray-900"><?php echo e(auth()->user()->name); ?></p>
                        <select name="organization_id" class="text-sm text-gray-600 border-0 bg-transparent focus:ring-0 p-0">
                            <option value="">Personal Post</option>
                            <?php $__currentLoopData = $organizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $org): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($org->id); ?>" <?php echo e($post->organization_id == $org->id ? 'selected' : ''); ?>><?php echo e($org->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                </div>

                <!-- Tags -->
                <?php if($post->postMethod && $post->postMethod->activeTags->count() > 0): ?>
                    <div class="mb-6" id="edit_tags_section">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tags (<?php echo e($post->postMethod->name); ?>)</label>
                        <div id="edit_tags_container" class="space-y-2">
                            <?php $__currentLoopData = $post->postMethod->activeTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center">
                                    <input type="checkbox" name="tags[]" value="<?php echo e($tag->id); ?>" id="edit_tag_<?php echo e($tag->id); ?>"
                                           class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded"
                                           <?php echo e($post->tags->contains($tag->id) ? 'checked' : ''); ?>>
                                    <label for="edit_tag_<?php echo e($tag->id); ?>" class="ml-2 text-sm text-gray-700 flex items-center">
                                        <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: <?php echo e($tag->color); ?>"></span>
                                        <?php echo e($tag->name); ?>

                                    </label>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php $__errorArgs = ['tags'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                <?php endif; ?>

                 <!-- Visibility -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Who can see this?</label>
                    <select name="visibility" id="edit_visibility_select" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" onchange="updateEditVisibilityDescription()">
                        <?php if($post->group_id): ?>
                            <!-- Group post options -->
                            <option value="group_members_only" <?php echo e(old('visibility', $post->visibility) == 'group_members_only' ? 'selected' : ''); ?>>👥 Group Members Only</option>
                            <option value="public" <?php echo e(old('visibility', $post->visibility) == 'public' ? 'selected' : ''); ?>>🌐 Public</option>
                        <?php elseif($post->organization_id): ?>
                            <!-- Organization post options -->
                            <option value="same_school_only" <?php echo e(old('visibility', $post->visibility) == 'same_school_only' ? 'selected' : ''); ?>>🏫 Same school only</option>
                            <option value="public" <?php echo e(old('visibility', $post->visibility) == 'public' ? 'selected' : ''); ?>>🌐 Public</option>
                            <option value="same_campus_only" <?php echo e(old('visibility', $post->visibility) == 'same_campus_only' ? 'selected' : ''); ?>>🏛️ Same campus only</option>
                            <option value="organization_only" <?php echo e(old('visibility', $post->visibility) == 'organization_only' ? 'selected' : ''); ?>>🏢 My Organization Only / Members</option>
                            <option value="officers_only" <?php echo e(old('visibility', $post->visibility) == 'officers_only' ? 'selected' : ''); ?>>🔒 Officer Only</option>
                        <?php else: ?>
                            <!-- Personal post options -->
                            <option value="followers_only" <?php echo e(old('visibility', $post->visibility) == 'followers_only' ? 'selected' : ''); ?>>🤝 Followers Only</option>
                            <option value="public" <?php echo e(old('visibility', $post->visibility) == 'public' ? 'selected' : ''); ?>>🌐 Public</option>
                            <option value="same_school_only" <?php echo e(old('visibility', $post->visibility) == 'same_school_only' ? 'selected' : ''); ?>>🏫 Same school only</option>
                            <option value="same_campus_only" <?php echo e(old('visibility', $post->visibility) == 'same_campus_only' ? 'selected' : ''); ?>>🏛️ Same campus only</option>
                            <option value="private" <?php echo e(old('visibility', $post->visibility) == 'private' ? 'selected' : ''); ?>>👤 Only Me / Private</option>
                        <?php endif; ?>
                    </select>
                    <p class="text-xs text-gray-500 mt-1" id="edit_visibility_description">
                        <?php switch(old('visibility', $post->visibility)):
                            case ('public'): ?>
                                Anyone on or off UniLink can see this post
                                <?php break; ?>
                            <?php case ('followers_only'): ?>
                                Only people who follow you can see this post
                                <?php break; ?>
                            <?php case ('same_school_only'): ?>
                                Only people from your school can see this post
                                <?php break; ?>
                            <?php case ('same_campus_only'): ?>
                                Only people from your campus can see this post
                                <?php break; ?>
                            <?php case ('private'): ?>
                                Only you can see this post
                                <?php break; ?>
                            <?php case ('organization_only'): ?>
                                Only members of your organization can see this post
                                <?php break; ?>
                            <?php case ('officers_only'): ?>
                                Only officers of your organization can see this post
                                <?php break; ?>
                            <?php case ('group_members_only'): ?>
                                Only members of this group can see this post
                                <?php break; ?>
                            <?php default: ?>
                                Anyone on or off UniLink can see this post
                        <?php endswitch; ?>
                    </p>
                    <?php $__errorArgs = ['visibility'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Title -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input type="text" name="title" value="<?php echo e(old('title', $post->title)); ?>" placeholder="What's the title of your post?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green text-lg" required>
                    <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Content -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                    <textarea name="content" rows="6" placeholder="What's on your mind?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" required><?php echo e(old('content', $post->content)); ?></textarea>
                    <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Existing Images -->
                <?php if($post->images && count($post->images) > 0): ?>
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Images</label>
                        <div class="grid grid-cols-2 gap-2">
                            <?php $__currentLoopData = $post->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="relative">
                                    <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>"
                                         class="w-full h-24 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                         onclick="openImageModal(<?php echo e(json_encode($post->images)); ?>, <?php echo e($index); ?>)">
                                    <label class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 cursor-pointer z-10">
                                        <input type="checkbox" name="remove_images[]" value="<?php echo e($image); ?>" class="hidden" onchange="toggleImageRemoval(this)">
                                        ×
                                    </label>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Click images to view full size, or click × to mark for removal</p>
                    </div>
                <?php endif; ?>

                <!-- New Image Upload -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Add New Images (Optional)</label>
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center space-x-2 cursor-pointer text-custom-green hover:text-custom-second-darkest">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                            </svg>
                            <span class="text-sm font-medium">Add Photos</span>
                            <input type="file" name="images[]" multiple accept="image/*" class="hidden" onchange="previewImages(this)">
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">You can upload up to 5 images. Max 2MB each.</p>
                    
                    <!-- New Image Preview -->
                    <div id="image-preview" class="mt-3 grid grid-cols-2 gap-2 hidden"></div>
                    
                    <?php $__errorArgs = ['images'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <?php $__errorArgs = ['images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Existing Attachments -->
                <?php if($post->fileAttachments && $post->fileAttachments->count() > 0): ?>
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current File Attachments</label>
                        <div class="space-y-2">
                            <?php $__currentLoopData = $post->fileAttachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors attachment-item" data-attachment-id="<?php echo e($attachment->id); ?>">
                                    <div class="flex-shrink-0">
                                        <i class="<?php echo e(app('App\Services\FileUploadService')->getFileTypeIcon($attachment->file_type)); ?> text-gray-500"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($attachment->original_filename); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo e($attachment->formatted_size); ?></p>
                                    </div>
                                    <div class="flex-shrink-0 flex items-center space-x-2">
                                        <a href="<?php echo e($attachment->url); ?>" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                            Download
                                        </a>
                                        <label class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 cursor-pointer">
                                            <input type="checkbox" name="remove_attachments[]" value="<?php echo e($attachment->id); ?>" class="hidden" onchange="toggleAttachmentRemoval(this)">
                                            ×
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Click × to mark attachments for removal</p>
                    </div>
                <?php endif; ?>

                <!-- New Attachment Upload -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Add New File Attachments (Optional)</label>
                    <?php if($post->group_id): ?>
                        <?php
                            $group = $post->group;
                        ?>
                        <?php if($group && $group->allow_file_sharing): ?>
                            <!-- Group post attachments with group-specific settings -->
                            <?php if (isset($component)) { $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-upload-zone','data' => ['name' => 'attachments','accept' => ''.e($group->allowed_file_types ? '.' . implode(',.', $group->allowed_file_types) : '*').'','multiple' => true,'maxSize' => ''.e($group->max_file_size_mb).'MB','allowedTypes' => $group->allowed_file_types,'title' => 'Upload Files','description' => 'Drag and drop files here or click to browse']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-upload-zone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'attachments','accept' => ''.e($group->allowed_file_types ? '.' . implode(',.', $group->allowed_file_types) : '*').'','multiple' => true,'max-size' => ''.e($group->max_file_size_mb).'MB','allowed-types' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($group->allowed_file_types),'title' => 'Upload Files','description' => 'Drag and drop files here or click to browse']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $attributes = $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $component = $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
                        <?php else: ?>
                            <!-- Group doesn't allow file sharing -->
                            <div class="text-sm text-gray-500 italic">
                                File attachments are not allowed in this group.
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <!-- Normal post attachments with default settings -->
                        <?php if (isset($component)) { $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-upload-zone','data' => ['name' => 'attachments','accept' => '.pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.zip,.rar,.7z','multiple' => true,'maxSize' => '10MB','allowedTypes' => ['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z'],'title' => 'Upload Files','description' => 'Drag and drop files here or click to browse (PDF, DOC, images, videos, archives - max 10MB)']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-upload-zone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'attachments','accept' => '.pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.zip,.rar,.7z','multiple' => true,'max-size' => '10MB','allowed-types' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z']),'title' => 'Upload Files','description' => 'Drag and drop files here or click to browse (PDF, DOC, images, videos, archives - max 10MB)']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $attributes = $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $component = $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
                    <?php endif; ?>
                    <?php $__errorArgs = ['attachments'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <?php $__errorArgs = ['attachments.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Facebook Embed URL -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Facebook Embed URL (Optional)</label>
                    <input type="url" name="facebook_embed_url" value="<?php echo e(old('facebook_embed_url', $post->facebook_embed_url)); ?>" placeholder="https://www.facebook.com/..." class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                    <?php $__errorArgs = ['facebook_embed_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Post Options -->
                <div class="mb-6">
                    <div class="flex items-center space-x-6">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_pinned" value="1" <?php echo e($post->is_pinned ? 'checked' : ''); ?> class="rounded border-gray-300 text-custom-green shadow-sm focus:border-custom-green focus:ring focus:ring-custom-green focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Pin this post</span>
                        </label>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" required>
                        <option value="published" <?php echo e($post->status == 'published' ? 'selected' : ''); ?>>Published</option>
                        <option value="draft" <?php echo e($post->status == 'draft' ? 'selected' : ''); ?>>Draft</option>
                    </select>
                    <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4">
                <a href="<?php echo e(route('dashboard')); ?>" class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-custom-green hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                    Update Post
                </button>
            </div>
        </form>
    </div>

    <script>
    function toggleImageRemoval(checkbox) {
        const container = checkbox.closest('.relative');
        const img = container.querySelector('img');

        if (checkbox.checked) {
            img.style.opacity = '0.5';
            container.style.border = '2px solid #ef4444';
        } else {
            img.style.opacity = '1';
            container.style.border = 'none';
        }
    }

    function toggleAttachmentRemoval(checkbox) {
        const container = checkbox.closest('.attachment-item');

        if (checkbox.checked) {
            container.style.opacity = '0.5';
            container.style.border = '2px solid #ef4444';
            container.style.backgroundColor = '#fee2e2';
        } else {
            container.style.opacity = '1';
            container.style.border = 'none';
            container.style.backgroundColor = '#f9fafb';
        }
    }

    function previewImages(input) {
        const preview = document.getElementById('image-preview');
        preview.innerHTML = '';
        
        if (input.files && input.files.length > 0) {
            preview.classList.remove('hidden');
            
            Array.from(input.files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'relative';
                        div.innerHTML = `
                            <img src="${e.target.result}" class="w-full h-24 object-cover rounded-lg">
                            <button type="button" onclick="removeNewImage(${index}, this)" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                ×
                            </button>
                        `;
                        preview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                }
            });
        } else {
            preview.classList.add('hidden');
        }
    }

    function removeNewImage(index, button) {
        const input = document.querySelector('input[name="images[]"]');
        const dt = new DataTransfer();
        
        Array.from(input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        input.files = dt.files;
        button.parentElement.remove();
        
        if (input.files.length === 0) {
            document.getElementById('image-preview').classList.add('hidden');
        }
    }

    // Update visibility description based on selection
    function updateEditVisibilityDescription() {
        const visibilitySelect = document.getElementById('edit_visibility_select');
        const descriptionElement = document.getElementById('edit_visibility_description');

        const descriptions = {
            'public': 'Anyone on or off UniLink can see this post',
            'followers_only': 'Only people who follow you can see this post',
            'university_only': 'Only people from your university can see this post',
            'private': 'Only you can see this post',
            'organization_only': 'Only members of your organization can see this post',
            'officers_only': 'Only officers of your organization can see this post',
            'group_members_only': 'Only members of this group can see this post'
        };

        descriptionElement.textContent = descriptions[visibilitySelect.value] || 'Anyone on or off UniLink can see this post';
    }

    // Tags are now loaded automatically based on context, no JavaScript needed
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/posts/edit.blade.php ENDPATH**/ ?>