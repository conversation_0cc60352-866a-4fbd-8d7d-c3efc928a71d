<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\Organization;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;

class SuggestedConnections extends Component
{
    public $suggestedUsers = [];
    public $isLoading = true;
    public $refreshKey = 0;
    public $silentUpdate = false; // Flag to prevent loading indicator during follow actions

    public function mount()
    {
        // Load suggestions immediately without loading state to prevent flickering
        $this->isLoading = false;
        $this->loadSuggestedUsers();
    }

    public function loadSuggestedUsers()
    {
        if (!Auth::check()) {
            $this->suggestedUsers = [];
            $this->isLoading = false;
            return;
        }

        // Set loading to false immediately to prevent flickering on initial load
        if (!$this->silentUpdate) {
            $this->isLoading = false;
        }

        // Don't show loading indicator during silent updates (follow actions)
        if (!$this->silentUpdate) {
            $this->isLoading = true;
        }

        $currentUser = Auth::user();
        $allSuggestions = collect();
        $seenUserIds = collect();

        // Get users already being followed to exclude them
        $followingIds = $currentUser->following()->pluck('users.id')->toArray();
        $followingIds[] = $currentUser->id; // Exclude self

        // 1. Find users from same organizations
        $organizationSuggestions = $this->getUsersFromSameOrganizations($currentUser, $followingIds);
        foreach ($organizationSuggestions as $user) {
            if (!$seenUserIds->contains($user->id)) {
                $allSuggestions->push($user);
                $seenUserIds->push($user->id);
            }
        }

        // 2. Find users from same campus and course
        $academicSuggestions = $this->getUsersFromSameAcademicBackground($currentUser, $followingIds);
        foreach ($academicSuggestions as $user) {
            if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < 5) {
                $allSuggestions->push($user);
                $seenUserIds->push($user->id);
            }
        }

        // 3. Find users with similar skills/interests
        $skillsSuggestions = $this->getUsersWithSimilarSkills($currentUser, $followingIds);
        foreach ($skillsSuggestions as $user) {
            if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < 5) {
                $allSuggestions->push($user);
                $seenUserIds->push($user->id);
            }
        }

        // 4. Find popular users (fallback)
        if ($allSuggestions->count() < 5) {
            $excludeIds = array_merge($followingIds, $seenUserIds->toArray());
            $popularSuggestions = $this->getPopularUsers($currentUser, $excludeIds, 5 - $allSuggestions->count());
            foreach ($popularSuggestions as $user) {
                if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < 5) {
                    $allSuggestions->push($user);
                    $seenUserIds->push($user->id);
                }
            }
        }

        // Limit to 5 users and format the data
        $this->suggestedUsers = $allSuggestions
            ->take(5)
            ->map(function ($user) use ($currentUser) {
                return [
                    'user' => $user,
                    'shared_attributes' => $this->getSharedAttributes($currentUser, $user),
                    'is_following' => false // Will be updated by UserFollower component
                ];
            })
            ->toArray();

        $this->isLoading = false;
        $this->silentUpdate = false; // Reset silent update flag
    }

    /**
     * Load additional suggestions to fill up to 5 users (used after following someone)
     */
    public function loadAdditionalSuggestions()
    {
        if (!Auth::check()) {
            return;
        }

        $currentUser = Auth::user();
        $currentUserIds = collect($this->suggestedUsers)->pluck('user.id')->toArray();
        $followingIds = $currentUser->following()->pluck('users.id')->toArray();
        $followingIds[] = $currentUser->id; // Exclude self
        $excludeIds = array_merge($followingIds, $currentUserIds);

        $neededCount = 5 - count($this->suggestedUsers);
        if ($neededCount <= 0) {
            return;
        }

        $allSuggestions = collect();
        $seenUserIds = collect($excludeIds);

        // 1. Find users from same organizations
        $organizationSuggestions = $this->getUsersFromSameOrganizations($currentUser, $excludeIds);
        foreach ($organizationSuggestions as $user) {
            if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < $neededCount) {
                $allSuggestions->push($user);
                $seenUserIds->push($user->id);
            }
        }

        // 2. Find users from same campus and course
        if ($allSuggestions->count() < $neededCount) {
            $academicSuggestions = $this->getUsersFromSameAcademicBackground($currentUser, $seenUserIds->toArray());
            foreach ($academicSuggestions as $user) {
                if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < $neededCount) {
                    $allSuggestions->push($user);
                    $seenUserIds->push($user->id);
                }
            }
        }

        // 3. Find users with similar skills/interests
        if ($allSuggestions->count() < $neededCount) {
            $skillsSuggestions = $this->getUsersWithSimilarSkills($currentUser, $seenUserIds->toArray());
            foreach ($skillsSuggestions as $user) {
                if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < $neededCount) {
                    $allSuggestions->push($user);
                    $seenUserIds->push($user->id);
                }
            }
        }

        // 4. Find popular users (fallback)
        if ($allSuggestions->count() < $neededCount) {
            $popularSuggestions = $this->getPopularUsers($currentUser, $seenUserIds->toArray(), $neededCount - $allSuggestions->count());
            foreach ($popularSuggestions as $user) {
                if (!$seenUserIds->contains($user->id) && $allSuggestions->count() < $neededCount) {
                    $allSuggestions->push($user);
                    $seenUserIds->push($user->id);
                }
            }
        }

        // Add new suggestions to existing ones
        $newSuggestions = $allSuggestions->map(function ($user) use ($currentUser) {
            return [
                'user' => $user,
                'shared_attributes' => $this->getSharedAttributes($currentUser, $user),
                'is_following' => false // Will be updated by the view
            ];
        })->toArray();

        $this->suggestedUsers = array_merge($this->suggestedUsers, $newSuggestions);
        $this->silentUpdate = false; // Reset silent update flag
    }

    private function getUsersFromSameOrganizations(User $currentUser, array $excludeIds): Collection
    {
        $userOrganizations = $currentUser->organizations()->pluck('organizations.id');
        
        if ($userOrganizations->isEmpty()) {
            return collect();
        }

        return User::whereHas('organizations', function ($query) use ($userOrganizations) {
                $query->whereIn('organizations.id', $userOrganizations);
            })
            ->whereNotIn('id', $excludeIds)
            ->with(['organizations'])
            ->limit(10)
            ->get();
    }

    private function getUsersFromSameAcademicBackground(User $currentUser, array $excludeIds): Collection
    {
        $users = collect();

        // Same campus users
        if ($currentUser->campus) {
            $campusUsers = User::whereNotIn('id', $excludeIds)
                ->where('campus', $currentUser->campus)
                ->limit(5)
                ->get();
            $users = $users->merge($campusUsers);
        }

        // Same course program users
        if ($currentUser->course_program) {
            $courseUsers = User::whereNotIn('id', $excludeIds)
                ->where('course_program', $currentUser->course_program)
                ->limit(5)
                ->get();
            $users = $users->merge($courseUsers);
        }

        // Same college department users
        if ($currentUser->college_department) {
            $deptUsers = User::whereNotIn('id', $excludeIds)
                ->where('college_department', $currentUser->college_department)
                ->limit(5)
                ->get();
            $users = $users->merge($deptUsers);
        }

        return $users->unique('id')->take(10);
    }

    private function getUsersWithSimilarSkills(User $currentUser, array $excludeIds): Collection
    {
        $userSkills = $currentUser->skills_interests ?? [];
        
        if (empty($userSkills)) {
            return collect();
        }

        return User::whereNotIn('id', $excludeIds)
            ->whereNotNull('skills_interests')
            ->get()
            ->filter(function ($user) use ($userSkills) {
                $otherSkills = $user->skills_interests ?? [];
                $commonSkills = array_intersect($userSkills, $otherSkills);
                return count($commonSkills) > 0;
            })
            ->take(10);
    }

    private function getPopularUsers(User $currentUser, array $excludeIds, int $limit): Collection
    {
        return User::whereNotIn('id', $excludeIds)
            ->withCount('followers')
            ->orderBy('followers_count', 'desc')
            ->limit($limit)
            ->get();
    }

    private function getSharedAttributes(User $currentUser, User $otherUser): array
    {
        $shared = [];

        // Check for shared organizations
        $currentUserOrgs = $currentUser->organizations()->pluck('name')->toArray();
        $otherUserOrgs = $otherUser->organizations()->pluck('name')->toArray();
        $sharedOrgs = array_intersect($currentUserOrgs, $otherUserOrgs);
        
        if (!empty($sharedOrgs)) {
            $shared[] = 'Member of ' . implode(', ', array_slice($sharedOrgs, 0, 2));
        }

        // Check for same campus
        if ($currentUser->campus && $currentUser->campus === $otherUser->campus) {
            $shared[] = 'Same campus: ' . $currentUser->campus;
        }

        // Check for same course
        if ($currentUser->course_program && $currentUser->course_program === $otherUser->course_program) {
            $shared[] = 'Same course: ' . $currentUser->course_program;
        }

        // Check for similar skills
        $currentSkills = $currentUser->skills_interests ?? [];
        $otherSkills = $otherUser->skills_interests ?? [];
        $commonSkills = array_intersect($currentSkills, $otherSkills);
        
        if (!empty($commonSkills)) {
            $shared[] = 'Similar interests: ' . implode(', ', array_slice($commonSkills, 0, 2));
        }

        return $shared;
    }

    public function refreshSuggestions()
    {
        $this->silentUpdate = false; // Ensure loading indicator shows for manual refresh
        $this->isLoading = true;
        $this->suggestedUsers = [];
        $this->refreshKey++; // Increment to force component re-render

        // Force a re-render by dispatching a browser event
        $this->dispatch('refreshed');

        $this->loadSuggestedUsers();
    }

    /**
     * Listen for follow/unfollow events to refresh suggestions
     */
    protected $listeners = ['userFollowed' => 'handleUserFollowed', 'userUnfollowed' => 'handleUserUnfollowed'];

    public function handleUserFollowed($userId)
    {
        // Remove the followed user from suggestions and reindex array
        $this->suggestedUsers = array_values(array_filter($this->suggestedUsers, function($suggestion) use ($userId) {
            return $suggestion['user']->id !== $userId;
        }));

        // If we have less than 5 suggestions, load more silently (without loading indicator)
        if (count($this->suggestedUsers) < 5) {
            $this->silentUpdate = true;
            $this->loadAdditionalSuggestions();
        }
    }

    public function handleUserUnfollowed($userId)
    {
        // When someone is unfollowed, we could potentially add them back to suggestions
        // For now, just refresh to get new suggestions
        $this->silentUpdate = true; // Update silently without loading indicator
        $this->loadSuggestedUsers();
    }

    /**
     * Handle follow/unfollow action for suggested users
     */
    public function toggleFollow($userId)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $currentUser = Auth::user();

        // Prevent self-following
        if (Auth::id() === $userId) {
            session()->flash('error', 'You cannot follow yourself.');
            return;
        }

        // Find the user to follow/unfollow
        $userToToggle = User::find($userId);
        if (!$userToToggle) {
            session()->flash('error', 'User not found.');
            return;
        }

        try {
            $isFollowing = $currentUser->isFollowingUser($userToToggle);

            if ($isFollowing) {
                // Unfollow
                $currentUser->unfollowUser($userToToggle);
                session()->flash('success', "You have unfollowed {$userToToggle->name}.");

                // Dispatch event
                $this->dispatch('userUnfollowed', $userId);
            } else {
                // Follow
                $currentUser->followUser($userToToggle);
                session()->flash('success', "You are now following {$userToToggle->name}!");

                // Dispatch event
                $this->dispatch('userFollowed', $userId);
            }

            // Remove the followed user from suggestions and refresh if needed
            $this->handleUserFollowed($userId);

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while updating your follow status.');
        }
    }

    public function render()
    {
        return view('livewire.suggested-connections');
    }
}
