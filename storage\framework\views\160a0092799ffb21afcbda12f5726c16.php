<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['group' => null, 'organization' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['group' => null, 'organization' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<!-- Group/Organization Post Creation Modal -->
<div id="groupPostCreationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">
                    <?php if($group): ?>
                        Create Post in <?php echo e($group->name); ?>

                    <?php elseif($organization): ?>
                        Create Announcement for <?php echo e($organization->name); ?>

                    <?php else: ?>
                        Create Post
                    <?php endif; ?>
                </h3>
                <button onclick="closeGroupPostModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form action="<?php echo e(route('posts.store')); ?>" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            <?php echo csrf_field(); ?>
            
            <?php if($group): ?>
                <input type="hidden" name="group_id" value="<?php echo e($group->id); ?>">
            <?php elseif($organization): ?>
                <input type="hidden" name="organization_id" value="<?php echo e($organization->id); ?>">
            <?php endif; ?>

            <!-- User Info -->
            <div class="flex items-center space-x-3">
                <img class="h-10 w-10 rounded-full"
                     src="<?php echo e(auth()->user()->getAvatarUrl(40)); ?>"
                     alt="<?php echo e(auth()->user()->name); ?>">
                <div>
                    <p class="text-sm font-medium text-gray-900"><?php echo e(auth()->user()->name); ?></p>
                    <p class="text-xs text-gray-500">
                        <?php if($group): ?>
                            Posting to <?php echo e($group->name); ?>

                            <?php if($group->post_approval === 'required'): ?>
                                <span class="text-yellow-600">(Requires approval)</span>
                            <?php endif; ?>
                        <?php elseif($organization): ?>
                            Official announcement for <?php echo e($organization->name); ?>

                        <?php endif; ?>
                    </p>
                </div>
            </div>

        
             <!-- Visibility -->
            <div>
                <label for="group_visibility_select" class="block text-sm font-medium text-gray-700 mb-1">Who can see this?</label>
                <select name="visibility" id="group_visibility_select"
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        onchange="updateGroupVisibilityIcon()">
                    <option value="group_members_only" selected>👥 Group Members Only</option>
                    <option value="public">🌐 Public</option>
                </select>
                <p class="text-xs text-gray-500 mt-1" id="group_visibility_description">Only members of this group can see this post</p>
            </div>


            <!-- Tags -->
            <?php
                $postMethodSlug = 'user'; // Default
                if ($group) {
                    $postMethodSlug = 'group';
                } elseif ($organization) {
                    $postMethodSlug = 'organization';
                }
                $postMethod = \App\Models\PostMethod::where('slug', $postMethodSlug)->with('activeTags')->first();
            ?>
            
            <?php if($postMethod && $postMethod->activeTags->count() > 0): ?>
                <div id="group_modal_tags_section">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Post Tags (Select multiple)</label>
                    <div id="group_modal_tags_container" class="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                        <?php $__currentLoopData = $postMethod->activeTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                                <input type="checkbox" name="tags[]" value="<?php echo e($tag->id); ?>"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <div class="ml-3 flex items-center">
                                    <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: <?php echo e($tag->color); ?>"></span>
                                    <span class="text-sm text-gray-700"><?php echo e($tag->name); ?></span>
                                </div>
                            </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <p class="mt-2 text-xs text-gray-500">Select one or more tags that best describe your post</p>
                </div>
            <?php endif; ?>

            <!-- Title -->
            <div>
                <label for="group_title" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <input type="text" name="title" id="group_title"
                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                       placeholder="What's your post about?" required>
            </div>

            <!-- Content -->
            <div>
                <label for="group_content" class="block text-sm font-medium text-gray-700 mb-1">Content</label>
                <textarea name="content" id="group_content" rows="4"
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Share your thoughts..." required></textarea>
            </div>

            <!-- Image Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Images</label>
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                    <div class="space-y-1 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="flex text-sm text-gray-600">
                            <label for="group_images" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                <span>Upload images</span>
                                <input id="group_images" name="images[]" type="file" multiple accept="image/*" class="sr-only" onchange="previewGroupImages(this)">
                            </label>
                            <p class="pl-1">or drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB each</p>
                    </div>
                </div>
                <div id="group-image-preview" class="mt-4 hidden grid grid-cols-2 gap-4"></div>
            </div>

            <!-- File Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">File Attachments</label>
                <?php if($group && $group->allow_file_sharing): ?>
                    <!-- Group post attachments with group-specific settings -->
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="group_attachments" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                    <span>Upload files</span>
                                    <input id="group_attachments" name="attachments[]" type="file" multiple class="sr-only" onchange="previewGroupAttachments(this)">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500"><?php echo e($group->allowed_file_types ? implode(', ', $group->allowed_file_types) : 'Any file type'); ?> up to <?php echo e($group->max_file_size_mb); ?>MB each</p>
                        </div>
                    </div>
                    <div id="group-attachment-preview" class="mt-4 hidden space-y-2"></div>
                <?php elseif($group && !$group->allow_file_sharing): ?>
                    <!-- Group doesn't allow file sharing -->
                    <div class="text-sm text-gray-500 italic">
                        File attachments are not allowed in this group.
                    </div>
                <?php else: ?>
                    <!-- Default file upload for organizations or other contexts -->
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="group_attachments" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                    <span>Upload files</span>
                                    <input id="group_attachments" name="attachments[]" type="file" multiple class="sr-only" onchange="previewGroupAttachments(this)">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">Any file type up to 50MB each</p>
                        </div>
                    </div>
                    <div id="group-attachment-preview" class="mt-4 hidden space-y-2"></div>
                <?php endif; ?>
            </div>

            <!-- Facebook Embed URL -->
            <div>
                <label for="group_facebook_url" class="block text-sm font-medium text-gray-700 mb-1">Facebook Post URL (Optional)</label>
                <input type="url" name="facebook_embed_url" id="group_facebook_url"
                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                       placeholder="https://www.facebook.com/...">
            </div>

            <!-- Status -->
            <input type="hidden" name="status" value="published">

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeGroupPostModal()"
                        class="bg-gray-500 text-white px-4 py-2 rounded-md font-medium hover:bg-gray-400">
                    Cancel
                </button>
                <button type="submit"
                        class="bg-custom-green text-white px-6 py-2 rounded-md font-medium hover:bg-green-400">
                    <?php if($group && $group->post_approval === 'required'): ?>
                        Submit for Approval
                    <?php else: ?>
                        Post
                    <?php endif; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function openGroupPostModal() {
        document.getElementById('groupPostCreationModal').classList.remove('hidden');
        document.getElementById('groupPostCreationModal').classList.add('flex');
        document.body.style.overflow = 'hidden';
    }

    function closeGroupPostModal() {
        document.getElementById('groupPostCreationModal').classList.add('hidden');
        document.getElementById('groupPostCreationModal').classList.remove('flex');
        document.body.style.overflow = 'auto';

        // Reset form
        document.querySelector('#groupPostCreationModal form').reset();

        // Reset image preview
        const imagePreview = document.getElementById('group-image-preview');
        imagePreview.classList.add('hidden');
        imagePreview.innerHTML = '';

        // Reset attachment preview
        const attachmentPreview = document.getElementById('group-attachment-preview');
        attachmentPreview.classList.add('hidden');
        attachmentPreview.innerHTML = '';

        // Reset tag checkboxes
        const tagCheckboxes = document.querySelectorAll('#group_modal_tags_container input[type="checkbox"]');
        tagCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Reset visibility to default
        document.getElementById('group_visibility_select').value = 'group_members_only';
        updateGroupVisibilityIcon();
    }

    // Update visibility description based on selection
    function updateGroupVisibilityIcon() {
        const visibilitySelect = document.getElementById('group_visibility_select');
        const descriptionElement = document.getElementById('group_visibility_description');

        const descriptions = {
            'group_members_only': 'Only members of this group can see this post',
            'public': 'Anyone on or off UniLink can see this post'
        };

        descriptionElement.textContent = descriptions[visibilitySelect.value] || '';
    }

    // Image preview functions
    function previewGroupImages(input) {
        const preview = document.getElementById('group-image-preview');
        preview.innerHTML = '';

        if (input.files && input.files.length > 0) {
            preview.classList.remove('hidden');
            Array.from(input.files).forEach((file, index) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const div = document.createElement('div');
                        div.className = 'relative';
                        div.innerHTML = `
                            <img src="${e.target.result}" class="w-full h-32 object-cover rounded-lg">
                            <button type="button" onclick="removeGroupImage(${index}, this)" class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                ×
                            </button>
                        `;
                        preview.appendChild(div);
                    };
                    reader.readAsDataURL(file);
                }
            });
        } else {
            preview.classList.add('hidden');
        }
    }

    function previewGroupAttachments(input) {
        const preview = document.getElementById('group-attachment-preview');
        preview.innerHTML = '';

        if (input.files && input.files.length > 0) {
            preview.classList.remove('hidden');

            Array.from(input.files).forEach((file, index) => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg border';

                // Get file icon based on type
                let fileIcon = '📄'; // Default
                if (file.type.includes('pdf')) fileIcon = '📄';
                else if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) fileIcon = '📝';
                else if (file.type.includes('sheet') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) fileIcon = '📊';
                else if (file.type.includes('presentation') || file.name.endsWith('.ppt') || file.name.endsWith('.pptx')) fileIcon = '📊';

                fileDiv.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <span class="text-2xl">${fileIcon}</span>
                        <div>
                            <p class="text-sm font-medium text-gray-900">${file.name}</p>
                            <p class="text-xs text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                    </div>
                    <button type="button" onclick="removeGroupAttachment(${index})" class="text-red-500 hover:text-red-700">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                `;
                preview.appendChild(fileDiv);
            });
        } else {
            preview.classList.add('hidden');
        }
    }

    function removeGroupImage(index, button) {
        const input = document.querySelector('input[name="images[]"]');
        const dt = new DataTransfer();

        Array.from(input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });

        input.files = dt.files;
        button.parentElement.remove();

        if (input.files.length === 0) {
            document.getElementById('group-image-preview').classList.add('hidden');
        }
    }

    function removeGroupAttachment(index) {
        const input = document.querySelector('input[name="attachments[]"]');
        const dt = new DataTransfer();

        Array.from(input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });

        input.files = dt.files;

        // Remove the preview element
        const preview = document.getElementById('group-attachment-preview');
        const attachments = preview.children;
        if (attachments[index]) {
            attachments[index].remove();
        }

        // Check if preview is empty and hide if so
        if (preview.children.length === 0) {
            preview.classList.add('hidden');
        }
    }
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/group-post-creation-modal.blade.php ENDPATH**/ ?>