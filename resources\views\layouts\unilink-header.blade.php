<nav class="bg-white shadow-md border-b border-custom-second-darkest border-opacity-20 fixed w-full top-0 z-50">
    <div class="max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Left side - Logo and main nav -->
            <div class="flex items-center">
                <!-- Mobile menu button -->
                <button type="button" class="lg:hidden inline-flex items-center justify-center p-2 rounded-md text-custom-second-darkest hover:text-custom-darkest hover:bg-custom-lightest focus:outline-none focus:ring-2 focus:ring-inset focus:ring-custom-green" x-data x-on:click="$dispatch('toggle-sidebar')">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>

                <!-- Logo -->
                <div class="flex-shrink-0 flex items-center ml-4 lg:ml-0">
                    <a href="{{ route('dashboard') }}" class="text-2xl font-bold">
                        <span class="text-custom-green">Uni</span><span class="text-custom-darkest">Link</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:ml-10 lg:flex lg:space-x-8">
                    <a href="{{ route('dashboard') }}" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('dashboard') ? 'text-custom-green bg-custom-lightest' : '' }}">
                        Home
                    </a>
                    <a href="{{ route('organizations.my') }}" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('organizations*') ? 'text-custom-green bg-custom-lightest' : '' }}">
                        My Organizations
                    </a>
                    <a href="{{ route('groups.my') }}" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium {{ request()->routeIs('groups*') ? 'text-custom-green bg-custom-lightest' : '' }}">
                        My Groups
                    </a>
                    <!-- Quick Links Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="text-custom-darkest hover:text-custom-green px-3 py-2 rounded-md text-sm font-medium flex items-center">
                            Quick Links
                            <svg class="ml-1 h-4 w-4 transition-transform" :class="open ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Quick Links dropdown menu -->
                        <div style="display: none;" x-show="open" @click.away="open = false" x-transition class="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg py-1 z-50 border border-custom-second-darkest border-opacity-20">
                            <div class="px-4 py-2 text-xs font-semibold text-custom-second-darkest uppercase tracking-wider border-b border-gray-100">
                                Academic Resources
                            </div>
                            <a href="{{ route('scholarships.index') }}" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest hover:text-custom-green transition-colors {{ request()->routeIs('scholarships*') ? 'text-custom-green bg-custom-lightest' : '' }}">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-3 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                    Find Scholarships
                                </div>
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest hover:text-custom-green transition-colors">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                                    </svg>
                                    University Website
                                </div>
                            </a>
                            <a href="http://isulancampus.sksu.edu.ph/myschool/" target="_blank" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest hover:text-custom-green transition-colors">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Student Portal
                                </div>
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest hover:text-custom-green transition-colors">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-3 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                    Library Resources
                                </div>
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest hover:text-custom-green transition-colors">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L14 7" />
                                    </svg>
                                    Academic Calendar
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Center - Search -->
            <div class="flex-1 max-w-2xl mx-4 lg:mx-8 hidden sm:block">
                <div class="relative" id="header-search-container">
                    <input type="text"
                           id="header-search-input"
                           placeholder="Search users..."
                           class="w-full pl-10 pr-4 py-2 bg-custom-lightest border-0 rounded-full focus:ring-2 focus:ring-custom-green focus:bg-white focus:shadow-md transition-all"
                           autocomplete="off">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-custom-second-darkest" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>

                    <!-- Search Results Dropdown -->
                    <div id="header-search-results"
                         class="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 max-h-96 overflow-y-auto z-50 hidden">
                        <div id="search-results-content">
                            <!-- Results will be populated here -->
                        </div>
                        <div id="search-loading" class="hidden p-4 text-center text-gray-500">
                            <svg class="animate-spin h-5 w-5 mx-auto" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="ml-2">Searching...</span>
                        </div>
                        <div id="search-no-results" class="hidden p-4 text-center text-gray-500">
                            No users found
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Search Button -->
            <div class="sm:hidden">
                <button class="p-2 text-custom-second-darkest hover:text-custom-darkest focus:outline-none focus:ring-2 focus:ring-custom-green rounded-full">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </div>

            <!-- Right side - notifications, profile -->
            <div class="flex items-center space-x-4">

                <!-- Notifications -->
                <div class="relative" x-data="notificationDropdown()" x-init="init()">
                    <button @click="toggleDropdown()" class="relative p-2 text-custom-second-darkest hover:text-custom-darkest focus:outline-none focus:ring-2 focus:ring-custom-green focus:ring-offset-2 rounded-full">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <!-- Improved bell icon -->
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8a6 6 0 10-12 0c0 7-3 9-3 9h18s-3-2-3-9" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.73 21a2 2 0 01-3.46 0" />
                        </svg>
                        <!-- Notification badge -->
                        <span x-show="unreadCount > 0" x-text="unreadCount > 99 ? '99+' : unreadCount" class="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[18px] h-[18px]"></span>
                    </button>

                    <!-- Notification dropdown -->
                    <div style="display: none;" x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-96 bg-white rounded-md shadow-lg py-1 z-50 border border-custom-second-darkest border-opacity-20">
                        <!-- Header -->
                        <div class="px-4 py-3 border-b border-custom-second-darkest border-opacity-20">
                            <div class="flex justify-between items-center">
                                <h3 class="text-sm font-medium text-custom-darkest">Notifications</h3>
                                <div class="flex items-center space-x-2">
                                    <!-- Browser notification permission button -->
                                    <button @click="requestNotificationPermission()"
                                            x-show="'Notification' in window && Notification.permission === 'default'"
                                            class="text-xs text-gray-500 hover:text-custom-green"
                                            title="Enable browser notifications">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                                        </svg>
                                    </button>
                                    <!-- Mark all as read button -->
                                    <button @click="markAllAsRead()" x-show="unreadCount > 0" class="text-xs text-custom-green hover:text-custom-darkest">
                                        Mark all as read
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Browser notification permission prompt -->
                        <div x-show="showPermissionPrompt" x-transition class="px-4 py-3 bg-blue-50 border-b border-blue-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-blue-800 font-medium">Enable Browser Notifications</p>
                                    <p class="text-xs text-blue-600 mt-1">Get notified even when UniLink is not open</p>
                                    <div class="flex space-x-2 mt-2">
                                        <button @click="requestNotificationPermission()"
                                                class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">
                                            Enable
                                        </button>
                                        <button @click="dismissPermissionPrompt()"
                                                class="text-xs text-blue-600 hover:text-blue-800">
                                            Not now
                                        </button>
                                    </div>
                                </div>
                                <button @click="dismissPermissionPrompt()" class="flex-shrink-0 text-blue-400 hover:text-blue-600">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Notifications list -->
                        <div class="max-h-96 overflow-y-auto">
                            <template x-if="loading">
                                <div class="px-4 py-8 text-center">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-custom-green mx-auto"></div>
                                    <p class="text-sm text-custom-second-darkest mt-2">Loading notifications...</p>
                                </div>
                            </template>

                            <template x-if="!loading && notifications.length === 0">
                                <div class="px-4 py-8 text-center">
                                    <svg class="mx-auto h-12 w-12 text-custom-second-darkest opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v6m0 0v6m0-6h6m-6 0H11" />
                                    </svg>
                                    <p class="text-sm text-custom-second-darkest mt-2">No notifications yet</p>
                                </div>
                            </template>

                            <template x-for="notification in notifications" :key="notification.id">
                                <div @click="markAsRead(notification.id, notification.data.url)"
                                     :class="getNotificationClasses(notification)"
                                     class="px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 transition-all duration-200 hover:shadow-sm">
                                    <div class="flex items-start space-x-3">
                                        <!-- Notification Type Icon -->
                                        <div class="flex-shrink-0 relative">
                                            <!-- Avatar -->
                                            <img :src="notification.data.user_avatar || notification.data.group_logo || notification.data.organization_logo || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(notification.data.user_name || notification.data.group_name || notification.data.organization_name || 'User') + '&color=7BC74D&background=EEEEEE&size=64'"
                                                 :alt="notification.data.user_name || notification.data.group_name || notification.data.organization_name"
                                                 class="h-10 w-10 rounded-full object-cover border-2"
                                                 :class="getAvatarBorderClass(notification)"
                                                 onerror="this.src = 'https://ui-avatars.com/api/?name=' + encodeURIComponent(this.alt || 'User') + '&color=7BC74D&background=EEEEEE&size=64'">

                                            <!-- Type Icon Overlay -->
                                            <div class="absolute -bottom-1 -right-1 rounded-full p-1 shadow-sm"
                                                 :class="getIconBackgroundClass(notification)">
                                                <div x-html="getNotificationIcon(notification)" class="h-3 w-3"></div>
                                            </div>
                                        </div>

                                        <!-- Content -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <p class="text-sm font-medium"
                                                       :class="getTextClass(notification)"
                                                       x-html="notification.data.message"></p>
                                                    <p class="text-xs text-gray-500 mt-1" x-text="notification.time_ago"></p>

                                                    <!-- Additional context for certain notification types -->
                                                    <div x-show="notification.data.reaction_emoji" class="mt-1">
                                                        <span class="text-lg" x-text="notification.data.reaction_emoji"></span>
                                                    </div>
                                                </div>

                                                <!-- Unread indicator -->
                                                <div x-show="!notification.read_at" class="flex-shrink-0 ml-2">
                                                    <div class="h-2 w-2 rounded-full"
                                                         :class="getUnreadIndicatorClass(notification)"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Profile dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-custom-green focus:ring-offset-2">
                        <img class="h-8 w-8 rounded-full" src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
                    </button>

                    <!-- Profile dropdown menu -->
                    <div style="display: none;" x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-custom-second-darkest border-opacity-20">
                        <div class="px-4 py-2 text-sm text-custom-darkest border-b border-custom-second-darkest border-opacity-20">
                            <div class="font-medium">{{ auth()->user()->name }}</div>
                            <div class="text-custom-second-darkest">{{ auth()->user()->email }}</div>
                        </div>
                        <a href="{{ route('profile.show') }}" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">Profile</a>
                        @if(auth()->user()->hasManagementAccess())
                            <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">Admin Panel</a>
                        @endif
                        <form method="POST" action="{{ route('logout') }}" onsubmit="handleLogout(event)">
                            @csrf
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-custom-darkest hover:bg-custom-lightest">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
function notificationDropdown() {
    return {
        open: false,
        loading: false,
        notifications: [],
        unreadCount: 0,
        showPermissionPrompt: false,
        lastLoadTime: null,

        init() {
            this.loadNotifications();
            this.loadUnreadCount();
            this.setupRealTimeUpdates();
        },

        toggleDropdown() {
            this.open = !this.open;
            if (this.open) {
                // Only reload notifications if they haven't been loaded recently (within 30 seconds)
                const now = Date.now();
                if (!this.lastLoadTime || (now - this.lastLoadTime) > 30000) {
                    this.loadNotifications();
                }
                this.checkNotificationPermission();
            }
        },

        checkNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                // Show permission prompt after a short delay
                setTimeout(() => {
                    this.showPermissionPrompt = true;
                }, 1000);
            }
        },

        async loadNotifications() {
            this.loading = true;
            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    console.error('CSRF token not found');
                    return;
                }

                const response = await fetch('/notifications', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                this.notifications = data.notifications || [];
                this.unreadCount = data.unread_count || 0;
                this.lastLoadTime = Date.now(); // Track when notifications were last loaded
            } catch (error) {
                console.error('Error loading notifications:', error);
                this.notifications = [];
                this.unreadCount = 0;
            } finally {
                this.loading = false;
            }
        },

        async loadUnreadCount() {
            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    console.error('CSRF token not found');
                    return;
                }

                const response = await fetch('/notifications/unread-count', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success !== false) {
                    this.unreadCount = data.unread_count || 0;
                } else {
                    console.error('API returned error:', data.error);
                    this.unreadCount = 0;
                }
            } catch (error) {
                console.error('Error loading unread count:', error);
                // Set to 0 to prevent UI issues
                this.unreadCount = 0;
            }
        },

        async markAsRead(notificationId, url = null) {
            try {
                const response = await fetch(`/notifications/${notificationId}/read`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Update the notification in the list
                    const notification = this.notifications.find(n => n.id === notificationId);
                    if (notification) {
                        notification.read_at = new Date().toISOString();
                    }
                    this.unreadCount = data.unread_count;

                    // Navigate to the URL if provided
                    if (url) {
                        this.open = false;
                        window.location.href = url;
                    }
                }
            } catch (error) {
                console.error('Error marking notification as read:', error);
            }
        },

        async markAllAsRead() {
            try {
                const response = await fetch('/notifications/read-all', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                const data = await response.json();

                if (data.success) {
                    // Mark all notifications as read in the list
                    this.notifications.forEach(notification => {
                        notification.read_at = new Date().toISOString();
                    });
                    this.unreadCount = 0;
                }
            } catch (error) {
                console.error('Error marking all notifications as read:', error);
            }
        },

        setupRealTimeUpdates() {
            if (window.Echo) {
                window.Echo.private(`App.Models.User.{{ auth()->id() }}`)
                    .notification((notification) => {
                        // Check if notification already exists by ID first
                        const existingById = this.notifications.find(n => n.id === notification.id);

                        if (existingById) {
                            return; // Skip if notification with same ID already exists
                        }

                        // Create a unique key for this notification as secondary check
                        const notificationKey = `${notification.type}-${notification.user_id}-${notification.post_id || ''}-${notification.comment_id || ''}-${notification.group_id || ''}`;

                        // Check if similar notification already exists to prevent duplicates
                        const existingNotification = this.notifications.find(n => {
                            const existingData = n.data || n;
                            const existingKey = `${existingData.type}-${existingData.user_id}-${existingData.post_id || ''}-${existingData.comment_id || ''}-${existingData.group_id || ''}`;

                            // Compare unique keys and check if they're within a reasonable time window
                            return existingKey === notificationKey &&
                                   Math.abs(new Date(n.created_at) - new Date(notification.created_at)) < 30000; // Within 30 seconds
                        });

                        if (!existingNotification) {
                            // Add new notification to the beginning of the list
                            this.notifications.unshift({
                                id: notification.id,
                                type: notification.type,
                                data: notification,
                                read_at: null,
                                created_at: notification.created_at,
                                time_ago: 'just now'
                            });

                            // Update unread count
                            this.unreadCount++;
                        }

                        // Always show browser notification for real-time events
                        this.showBrowserNotification(notification);
                    });
            }
        },

        showBrowserNotification(notification) {
            if (Notification.permission === 'granted') {
                // Prioritize real profile picture, fallback to generated avatar
                const iconUrl = notification.user_avatar ||
                    `https://ui-avatars.com/api/?name=${encodeURIComponent(notification.user_name || 'User')}&color=7BC74D&background=EEEEEE&size=64`;

                const browserNotification = new Notification('UniLink', {
                    body: notification.message.replace(/<[^>]*>/g, ''), // Strip HTML tags
                    icon: iconUrl,
                    badge: iconUrl,
                    tag: `notification-${notification.id}`, // Prevent duplicate notifications
                    requireInteraction: false,
                    silent: false
                });

                // Auto-close after 5 seconds
                setTimeout(() => {
                    browserNotification.close();
                }, 5000);

                // Handle click to navigate to the notification URL
                browserNotification.onclick = () => {
                    window.focus();
                    if (notification.url) {
                        window.location.href = notification.url;
                    }
                    browserNotification.close();
                };
            }
        },

        // Notification styling functions
        getNotificationClasses(notification) {
            const type = notification.data.type;
            const isRead = notification.read_at;

            const baseClasses = isRead ? 'bg-white' : '';
            const typeClasses = this.getNotificationTypeClasses(type, isRead);

            return `${baseClasses} ${typeClasses}`;
        },

        getNotificationTypeClasses(type, isRead) {
            const categories = {
                // Reactions - Orange theme
                'post_reacted': isRead ? 'hover:bg-orange-25' : 'bg-orange-50 border-l-4 border-orange-200',
                'comment_reacted': isRead ? 'hover:bg-orange-25' : 'bg-orange-50 border-l-4 border-orange-200',
                'share_reacted': isRead ? 'hover:bg-orange-25' : 'bg-orange-50 border-l-4 border-orange-200',

                // Social - Blue theme
                'user_followed': isRead ? 'hover:bg-blue-25' : 'bg-blue-50 border-l-4 border-blue-200',

                // Comments - Green theme
                'post_commented': isRead ? 'hover:bg-green-25' : 'bg-green-50 border-l-4 border-green-200',
                'comment_replied': isRead ? 'hover:bg-green-25' : 'bg-green-50 border-l-4 border-green-200',
                'share_commented': isRead ? 'hover:bg-green-25' : 'bg-green-50 border-l-4 border-green-200',

                // Shares - Purple theme
                'post_shared': isRead ? 'hover:bg-purple-25' : 'bg-purple-50 border-l-4 border-purple-200',

                // Group Activities - Indigo theme
                'group_member_joined': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                'group_membership_request': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                'group_post_created': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                'group_post_approved': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                'group_post_rejected': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                'group_post_pending': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',

                // Organization Activities - Gray theme
                'organization_post_created': isRead ? 'hover:bg-gray-25' : 'bg-gray-50 border-l-4 border-gray-200',
                'organization_request_submitted': isRead ? 'hover:bg-gray-25' : 'bg-gray-50 border-l-4 border-gray-200',
                'organization_request_approved': isRead ? 'hover:bg-gray-25' : 'bg-gray-50 border-l-4 border-gray-200',
                'organization_request_rejected': isRead ? 'hover:bg-gray-25' : 'bg-gray-50 border-l-4 border-gray-200',
            };

            return categories[type] || (isRead ? 'hover:bg-gray-50' : 'bg-blue-50');
        },

        getAvatarBorderClass(notification) {
            const type = notification.data.type;
            const borderClasses = {
                // Reactions - Orange
                'post_reacted': 'border-orange-300',
                'comment_reacted': 'border-orange-300',
                'share_reacted': 'border-orange-300',

                // Social - Blue
                'user_followed': 'border-blue-300',

                // Comments - Green
                'post_commented': 'border-green-300',
                'comment_replied': 'border-green-300',
                'share_commented': 'border-green-300',

                // Shares - Purple
                'post_shared': 'border-purple-300',

                // Group Activities - Indigo
                'group_member_joined': 'border-indigo-300',
                'group_membership_request': 'border-indigo-300',
                'group_post_created': 'border-indigo-300',
                'group_post_approved': 'border-indigo-300',
                'group_post_rejected': 'border-indigo-300',
                'group_post_pending': 'border-indigo-300',

                // Organization Activities - Gray
                'organization_post_created': 'border-gray-300',
                'organization_request_submitted': 'border-gray-300',
                'organization_request_approved': 'border-gray-300',
                'organization_request_rejected': 'border-gray-300',
            };

            return borderClasses[type] || 'border-gray-300';
        },

        getIconBackgroundClass(notification) {
            const type = notification.data.type;
            const bgClasses = {
                // Reactions - Orange
                'post_reacted': 'bg-orange-100',
                'comment_reacted': 'bg-orange-100',
                'share_reacted': 'bg-orange-100',

                // Social - Blue
                'user_followed': 'bg-blue-100',

                // Comments - Green
                'post_commented': 'bg-green-100',
                'comment_replied': 'bg-green-100',
                'share_commented': 'bg-green-100',

                // Shares - Purple
                'post_shared': 'bg-purple-100',

                // Group Activities - Indigo
                'group_member_joined': 'bg-indigo-100',
                'group_membership_request': 'bg-indigo-100',
                'group_post_created': 'bg-indigo-100',
                'group_post_approved': 'bg-indigo-100',
                'group_post_rejected': 'bg-indigo-100',
                'group_post_pending': 'bg-indigo-100',

                // Organization Activities - Gray
                'organization_post_created': 'bg-gray-100',
                'organization_request_submitted': 'bg-gray-100',
                'organization_request_approved': 'bg-gray-100',
                'organization_request_rejected': 'bg-gray-100',
            };

            return bgClasses[type] || 'bg-gray-100';
        },

        getTextClass(notification) {
            const type = notification.data.type;
            const textClasses = {
                // Reactions - Orange
                'post_reacted': 'text-orange-800',
                'comment_reacted': 'text-orange-800',
                'share_reacted': 'text-orange-800',

                // Social - Blue
                'user_followed': 'text-blue-800',

                // Comments - Green
                'post_commented': 'text-green-800',
                'comment_replied': 'text-green-800',
                'share_commented': 'text-green-800',

                // Shares - Purple
                'post_shared': 'text-purple-800',

                // Group Activities - Indigo
                'group_member_joined': 'text-indigo-800',
                'group_membership_request': 'text-indigo-800',
                'group_post_created': 'text-indigo-800',
                'group_post_approved': 'text-indigo-800',
                'group_post_rejected': 'text-indigo-800',
                'group_post_pending': 'text-indigo-800',

                // Organization Activities - Gray
                'organization_post_created': 'text-gray-800',
                'organization_request_submitted': 'text-gray-800',
                'organization_request_approved': 'text-gray-800',
                'organization_request_rejected': 'text-gray-800',
            };

            return textClasses[type] || 'text-gray-800';
        },

        getUnreadIndicatorClass(notification) {
            const type = notification.data.type;
            const indicatorClasses = {
                // Reactions - Orange
                'post_reacted': 'bg-orange-500',
                'comment_reacted': 'bg-orange-500',
                'share_reacted': 'bg-orange-500',

                // Social - Blue
                'user_followed': 'bg-blue-500',

                // Comments - Green
                'post_commented': 'bg-green-500',
                'comment_replied': 'bg-green-500',
                'share_commented': 'bg-green-500',

                // Shares - Purple
                'post_shared': 'bg-purple-500',

                // Group Activities - Indigo
                'group_member_joined': 'bg-indigo-500',
                'group_membership_request': 'bg-indigo-500',
                'group_post_created': 'bg-indigo-500',
                'group_post_approved': 'bg-indigo-500',
                'group_post_rejected': 'bg-indigo-500',
                'group_post_pending': 'bg-indigo-500',

                // Organization Activities - Gray
                'organization_post_created': 'bg-gray-500',
                'organization_request_submitted': 'bg-gray-500',
                'organization_request_approved': 'bg-gray-500',
                'organization_request_rejected': 'bg-gray-500',
            };

            return indicatorClasses[type] || 'bg-blue-500';
        },

        getNotificationIcon(notification) {
            const type = notification.data.type;
            const icons = {
                // Reactions - Heart/thumbs up with sparkle
                'post_reacted': '<svg class="text-orange-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/></svg>',
                'comment_reacted': '<svg class="text-orange-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/></svg>',
                'share_reacted': '<svg class="text-orange-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/></svg>',

                // Social - User plus
                'user_followed': '<svg class="text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/></svg>',

                // Comments - Chat bubble
                'post_commented': '<svg class="text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"/></svg>',
                'comment_replied': '<svg class="text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.707 3.293a1 1 0 010 1.414L5.414 7H11a7 7 0 017 7v2a1 1 0 11-2 0v-2a5 5 0 00-5-5H5.414l2.293 2.293a1 1 0 11-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/></svg>',
                'share_commented': '<svg class="text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"/></svg>',

                // Shares - Share/forward arrow
                'post_shared': '<svg class="text-purple-600" fill="currentColor" viewBox="0 0 20 20"><path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"/></svg>',

                // Group Activities - Users/community
                'group_member_joined': '<svg class="text-indigo-600" fill="currentColor" viewBox="0 0 20 20"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/></svg>',
                'group_membership_request': '<svg class="text-indigo-600" fill="currentColor" viewBox="0 0 20 20"><path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/></svg>',
                'group_post_created': '<svg class="text-indigo-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z" clip-rule="evenodd"/><path d="M15 7h1a2 2 0 012 2v5.5a1.5 1.5 0 01-3 0V9a1 1 0 00-1-1h-1V7z"/></svg>',
                'group_post_approved': '<svg class="text-indigo-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/></svg>',
                'group_post_rejected': '<svg class="text-indigo-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/></svg>',
                'group_post_pending': '<svg class="text-indigo-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/></svg>',

                // Organization Activities - Building/official
                'organization_post_created': '<svg class="text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"/></svg>',
                'organization_request_submitted': '<svg class="text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"/></svg>',
                'organization_request_approved': '<svg class="text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm7 4a1 1 0 10-2 0v3.586L9.707 8.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L13 9.586V6z" clip-rule="evenodd"/></svg>',
                'organization_request_rejected': '<svg class="text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"/></svg>',
            };

            return icons[type] || '<svg class="text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/></svg>';
        },

        requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        console.log('Notification permission granted');
                        this.showPermissionPrompt = false;

                        // Show a success message
                        this.showSuccessToast('Browser notifications enabled! You\'ll now receive notifications even when UniLink is closed.');
                    }
                });
            }
        },

        showSuccessToast(message) {
            // Create a temporary toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 max-w-sm';
            toast.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span class="text-sm">${message}</span>
                </div>
            `;

            document.body.appendChild(toast);

            // Remove after 5 seconds
            setTimeout(() => {
                toast.remove();
            }, 5000);
        },

        dismissPermissionPrompt() {
            this.showPermissionPrompt = false;
        }
    }
}
</script>

<!-- Header Search JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('header-search-input');
    const searchResults = document.getElementById('header-search-results');
    const searchContent = document.getElementById('search-results-content');
    const searchLoading = document.getElementById('search-loading');
    const searchNoResults = document.getElementById('search-no-results');
    let searchTimeout;

    if (searchInput) {
        // Handle search input
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length === 0) {
                hideSearchResults();
                return;
            }

            // Show loading state
            showSearchResults();
            showLoading();

            // Dynamic search with minimal delay
            const delay = query.length === 1 ? 100 : 50;
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, delay);
        });

        // Handle focus and blur events
        searchInput.addEventListener('focus', function() {
            if (this.value.trim().length > 0) {
                showSearchResults();
            }
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!document.getElementById('header-search-container').contains(e.target)) {
                hideSearchResults();
            }
        });

        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideSearchResults();
                this.blur();
            }
        });
    }

    function performSearch(query) {
        fetch(`/api/users/search?q=${encodeURIComponent(query)}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();

            if (data.success && data.users.length > 0) {
                displaySearchResults(data.users);
            } else {
                showNoResults();
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            hideLoading();
            showNoResults();
        });
    }

    function displaySearchResults(users) {
        searchContent.innerHTML = users.map(user => `
            <a href="${user.profile_url}"
               class="flex items-center p-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0">
                <img src="${user.avatar_url}"
                     alt="${user.name}"
                     class="w-10 h-10 rounded-full mr-3 object-cover">
                <div class="flex-1 min-w-0">
                    <div class="font-medium text-gray-900 truncate">${user.name}</div>
                    <div class="text-sm text-gray-500 truncate">
                        ${user.student_id ? user.student_id + ' • ' : ''}${user.role}
                    </div>
                </div>
            </a>
        `).join('');

        searchContent.classList.remove('hidden');
        searchNoResults.classList.add('hidden');
    }

    function showSearchResults() {
        searchResults.classList.remove('hidden');
    }

    function hideSearchResults() {
        searchResults.classList.add('hidden');
    }

    function showLoading() {
        searchLoading.classList.remove('hidden');
        searchContent.classList.add('hidden');
        searchNoResults.classList.add('hidden');
    }

    function hideLoading() {
        searchLoading.classList.add('hidden');
    }

    function showNoResults() {
        searchNoResults.classList.remove('hidden');
        searchContent.classList.add('hidden');
    }
});
</script>

