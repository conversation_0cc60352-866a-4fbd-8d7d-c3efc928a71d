<x-layouts.unilink-layout>
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Following</h1>
                    <p class="text-gray-600 mt-1">Manage your connections</p>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('follow-management.followers') }}" 
                       class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
                        View Followers
                    </a>
                    <a href="{{ route('profile.show') }}" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        Back to Profile
                    </a>
                </div>
            </div>
        </div>

        <!-- Tabs Navigation -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <a href="{{ route('follow-management.following', ['tab' => 'users']) }}" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors {{ $currentTab === 'users' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span>Users</span>
                            @if(isset($following))
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">{{ $following->total() }}</span>
                            @endif
                        </div>
                    </a>
                    <a href="{{ route('follow-management.following', ['tab' => 'organizations']) }}" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors {{ $currentTab === 'organizations' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            <span>Organizations</span>
                            @if(isset($organizations))
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">{{ $organizations->total() }}</span>
                            @endif
                        </div>
                    </a>
                    <a href="{{ route('follow-management.following', ['tab' => 'groups']) }}" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors {{ $currentTab === 'groups' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span>Groups</span>
                            @if(isset($groups))
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">{{ $groups->total() }}</span>
                            @endif
                        </div>
                    </a>
                    <a href="{{ route('follow-management.following', ['tab' => 'discover']) }}"
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors {{ $currentTab === 'discover' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        <div class="flex items-center space-x-2">
                            <x-svg-icon name="Discover_Users" class="w-5 h-5" />
                            <span>Discover Users</span>
                            @if(isset($allUsers))
                                <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs">{{ $allUsers->total() }}</span>
                            @endif
                        </div>
                    </a>
                </nav>
            </div>

            <!-- Search Bar -->
            <div class="p-6">
                <div class="flex items-center space-x-4">
                    <div class="flex-1">
                        <div class="relative" id="following-search-container">
                            <input type="text"
                                   id="following-search-input"
                                   placeholder="@if($currentTab === 'discover')Search users by name, email, or bio...@else Search {{ $currentTab }}...@endif"
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                   autocomplete="off">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <x-svg-icon name="Search" class="h-5 w-5 text-gray-400" />
                            </div>

                            <!-- Search Results Dropdown -->
                            <div id="following-search-results"
                                 class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 max-h-96 overflow-y-auto z-50 hidden">
                                <div id="following-search-loading" class="p-4 text-center text-gray-500 hidden">
                                    <div class="inline-flex items-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Searching...
                                    </div>
                                </div>
                                <div id="following-search-content"></div>
                            </div>
                        </div>
                    </div>
                    @if($search)
                        <a href="{{ route('follow-management.following', ['tab' => $currentTab]) }}"
                           class="text-gray-500 hover:text-gray-700 px-3 py-2 transition-colors">
                            Clear
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="bg-white rounded-lg shadow-sm">
            @if($currentTab === 'users' && isset($following))
                @include('follow-management.partials.users-tab', ['items' => $following])
            @elseif($currentTab === 'organizations' && isset($organizations))
                @include('follow-management.partials.organizations-tab', ['items' => $organizations])
            @elseif($currentTab === 'groups' && isset($groups))
                @include('follow-management.partials.groups-tab', ['items' => $groups])
            @elseif($currentTab === 'discover' && isset($allUsers))
                @include('follow-management.partials.discover-tab', ['items' => $allUsers])
            @endif
        </div>
    </div>
</x-layouts.unilink-layout>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('following-search-input');
    const searchResults = document.getElementById('following-search-results');
    const searchContent = document.getElementById('following-search-content');
    const searchLoading = document.getElementById('following-search-loading');
    let searchTimeout;
    let currentTab = '{{ $currentTab }}';

    function showSearchResults() {
        searchResults.classList.remove('hidden');
    }

    function hideSearchResults() {
        searchResults.classList.add('hidden');
    }

    function showLoading() {
        searchLoading.classList.remove('hidden');
        searchContent.innerHTML = '';
    }

    function hideLoading() {
        searchLoading.classList.add('hidden');
    }

    function performSearch(query) {
        fetch(`{{ route('follow-management.following.search') }}?q=${encodeURIComponent(query)}&tab=${currentTab}`)
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success && data.results.length > 0) {
                    let html = '';
                    data.results.forEach(result => {
                        if (result.type === 'user' || result.type === 'discover_user') {
                            html += `
                                <div class="p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                    <div class="flex items-center space-x-3">
                                        <img src="${result.avatar_url}"
                                             alt="${result.name}"
                                             class="w-10 h-10 rounded-full object-cover">
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">
                                                <a href="${result.profile_url}" class="hover:text-blue-600">
                                                    ${result.name}
                                                </a>
                                            </div>
                                            ${result.has_complete_profile ? `
                                                <div class="text-sm text-gray-600">${result.email}</div>
                                                ${result.bio ? `<div class="text-sm text-gray-500 mt-1">${result.bio.substring(0, 40)}${result.bio.length > 40 ? '...' : ''}</div>` : ''}
                                                ${result.type === 'discover_user' ? `<div class="text-xs text-gray-400 mt-1">${result.followers_count} followers</div>` : ''}
                                            ` : `
                                                <div class="text-sm text-gray-500">Basic profile</div>
                                            `}
                                        </div>
                                    </div>
                                </div>
                            `;
                        } else if (result.type === 'organization') {
                            html += `
                                <div class="p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                    <div class="flex items-center space-x-3">
                                        <img src="${result.logo_url}"
                                             alt="${result.name}"
                                             class="w-10 h-10 rounded-lg object-cover">
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">
                                                <a href="${result.profile_url}" class="hover:text-blue-600">
                                                    ${result.name}
                                                </a>
                                            </div>
                                            ${result.description ? `<div class="text-sm text-gray-600 mt-1">${result.description.substring(0, 100)}${result.description.length > 100 ? '...' : ''}</div>` : ''}
                                            <div class="text-xs text-gray-400 mt-1">${result.members_count} members</div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } else if (result.type === 'group') {
                            html += `
                                <div class="p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                                    <div class="flex items-center space-x-3">
                                        <img src="${result.logo_url}"
                                             alt="${result.name}"
                                             class="w-10 h-10 rounded-lg object-cover">
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">
                                                <a href="${result.profile_url}" class="hover:text-blue-600">
                                                    ${result.name}
                                                </a>
                                            </div>
                                            ${result.description ? `<div class="text-sm text-gray-600 mt-1">${result.description.substring(0, 100)}${result.description.length > 100 ? '...' : ''}</div>` : ''}
                                            <div class="text-xs text-gray-400 mt-1">${result.members_count} members</div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    });
                    searchContent.innerHTML = html;
                } else {
                    const tabName = currentTab === 'discover' ? 'users' : currentTab;
                    searchContent.innerHTML = `
                        <div class="p-4 text-center text-gray-500">
                            <div class="text-sm">No ${tabName} found matching "${query}"</div>
                        </div>
                    `;
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Search error:', error);
                searchContent.innerHTML = `
                    <div class="p-4 text-center text-red-500">
                        <div class="text-sm">Error searching. Please try again.</div>
                    </div>
                `;
            });
    }

    // Update current tab when tab links are clicked
    document.querySelectorAll('a[href*="tab="]').forEach(link => {
        link.addEventListener('click', function() {
            const url = new URL(this.href);
            const tab = url.searchParams.get('tab');
            if (tab) {
                currentTab = tab;
                // Update placeholder text
                const placeholders = {
                    'users': 'Search users...',
                    'organizations': 'Search organizations...',
                    'groups': 'Search groups...',
                    'discover': 'Search users by name, email, or bio...'
                };
                searchInput.placeholder = placeholders[tab] || 'Search...';
                // Clear search results
                hideSearchResults();
                searchInput.value = '';
            }
        });
    });

    if (searchInput) {
        // Handle search input
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length === 0) {
                hideSearchResults();
                return;
            }

            // Show loading state
            showSearchResults();
            showLoading();

            // Dynamic search with minimal delay
            const delay = query.length === 1 ? 300 : 150;
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, delay);
        });

        // Handle focus and blur events
        searchInput.addEventListener('focus', function() {
            if (this.value.trim().length > 0) {
                showSearchResults();
            }
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                hideSearchResults();
            }
        });

        // Handle escape key
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideSearchResults();
                this.blur();
            }
        });
    }
});
</script>
