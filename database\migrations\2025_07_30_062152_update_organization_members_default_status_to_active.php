<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organization_members', function (Blueprint $table) {
            // Change the default status from 'pending' to 'active'
            $table->enum('status', ['active', 'pending', 'inactive'])->default('active')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organization_members', function (Blueprint $table) {
            // Revert back to 'pending' as default
            $table->enum('status', ['active', 'pending', 'inactive'])->default('pending')->change();
        });
    }
};
