<?php

namespace App\View\Components\Layouts;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class UnilinkLayout extends Component
{
    public $bodyClass;
    public $mainBgClass;
    public $maxWidthClass;
    public $leftBorderClass;
    public $overlayClass;
    public $paddingClass;
    public $feedLayout;

    /**
     * Create a new component instance.
     */
    public function __construct(
        $bodyClass = null,
        $mainBgClass = null,
        $maxWidthClass = null,
        $leftBorderClass = null,
        $overlayClass = null,
        $paddingClass = null,
        $feedLayout = false
    ) {
        $this->bodyClass = $bodyClass;
        $this->mainBgClass = $mainBgClass;
        $this->maxWidthClass = $maxWidthClass;
        $this->leftBorderClass = $leftBorderClass;
        $this->overlayClass = $overlayClass;
        $this->paddingClass = $paddingClass;
        $this->feedLayout = $feedLayout;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.layouts.unilink-layout');
    }
}
