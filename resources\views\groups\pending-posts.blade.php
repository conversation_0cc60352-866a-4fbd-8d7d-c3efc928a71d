<x-layouts.unilink-layout>
    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Pending Posts</h1>
                    <p class="text-gray-600 mt-1">{{ $group->name }} - Posts awaiting approval</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('groups.show', $group) }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        Back to Group
                    </a>
                    <a href="{{ route('groups.members', $group) }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                        Manage Members
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Posts</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $pendingPosts->total() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Approved Posts</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $group->approvedPosts()->count() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-red-100 rounded-lg">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Rejected Posts</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $group->posts()->where('approval_status', 'rejected')->count() }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Posts List -->
        @if($pendingPosts->count() > 0)
            <div class="space-y-6">
                @foreach($pendingPosts as $post)
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <!-- Post Header -->
                        <div class="p-4 border-b border-gray-200 bg-yellow-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img class="h-10 w-10 rounded-full" src="{{ $post->user->avatar ? Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7F9CF5&background=EBF4FF' }}" alt="{{ $post->user->name }}">
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900">{{ $post->user->name }}</h3>
                                        <p class="text-sm text-gray-500">{{ $post->created_at->diffForHumans() }}</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending Approval
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ ucfirst($post->type) }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Post Content -->
                        <div class="p-4">
                            <h2 class="text-lg font-semibold text-gray-900 mb-2">{{ $post->title }}</h2>

                            <!-- Post Method and Tags -->
                            @if($post->postMethod || $post->tags->count() > 0)
                                <div class="mb-3">
                                    <div class="flex flex-wrap items-center gap-2">
                                        @if($post->postMethod)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ $post->postMethod->name }}
                                            </span>
                                        @endif
                                        @foreach($post->tags as $tag)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                                                  style="background-color: {{ $tag->color }}">
                                                {{ $tag->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <div class="text-gray-700 mb-4">
                                {!! nl2br(e($post->content)) !!}
                            </div>

                            <!-- Post Images -->
                            @if($post->images && count($post->images) > 0)
                                <div class="mb-4">
                                    @if(count($post->images) == 1)
                                        <!-- Single image - full width -->
                                        <div class="w-full">
                                            <img src="{{ Storage::disk('public')->url($post->images[0]) }}" alt="Post image" class="rounded-lg object-cover w-full max-h-96">
                                        </div>
                                    @elseif(count($post->images) == 2)
                                        <!-- Two images - side by side -->
                                        <div class="grid grid-cols-2 gap-2">
                                            @foreach($post->images as $image)
                                                <img src="{{ Storage::disk('public')->url($image) }}" alt="Post image" class="rounded-lg object-cover h-32 w-full">
                                            @endforeach
                                        </div>
                                    @elseif(count($post->images) == 3)
                                        <!-- Three images - first full width, others side by side -->
                                        <div class="space-y-2">
                                            <img src="{{ Storage::disk('public')->url($post->images[0]) }}" alt="Post image" class="rounded-lg object-cover w-full h-48">
                                            <div class="grid grid-cols-2 gap-2">
                                                @foreach(array_slice($post->images, 1) as $image)
                                                    <img src="{{ Storage::disk('public')->url($image) }}" alt="Post image" class="rounded-lg object-cover h-32 w-full">
                                                @endforeach
                                            </div>
                                        </div>
                                    @else
                                        <!-- Four or more images - responsive grid -->
                                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                                            @foreach($post->images as $image)
                                                <img src="{{ Storage::disk('public')->url($image) }}" alt="Post image" class="rounded-lg object-cover h-32 w-full">
                                            @endforeach
                                        </div>
                                    @endif
                                    <p class="text-sm text-gray-500 mt-2">{{ count($post->images) }} {{ count($post->images) == 1 ? 'image' : 'images' }}</p>
                                </div>
                            @endif

                            <!-- Post Attachments -->
                            @if($post->fileAttachments && $post->fileAttachments->count() > 0)
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Attachments:</h4>
                                    <div class="space-y-2">
                                        @foreach($post->fileAttachments as $attachment)
                                            <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
                                                <i class="{{ app('App\Services\FileUploadService')->getFileTypeIcon($attachment->file_type) }}"></i>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $attachment->original_filename }}</p>
                                                    <p class="text-xs text-gray-500">{{ $attachment->formatted_size }}</p>
                                                </div>
                                                <a href="{{ $attachment->url }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                                    Download
                                                </a>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Post Meta -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <div>
                                    <span>Posted {{ $post->created_at->format('M j, Y \a\t g:i A') }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Moderation Actions -->
                        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    <strong>Moderation Required:</strong> This post needs your approval before it becomes visible to group members.
                                </div>
                                
                                <div class="flex items-center space-x-3">
                                    <livewire:post-approval :post="$post" :group="$group" :key="'post-approval-'.$post->id" />
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $pendingPosts->links() }}
            </div>
        @else
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No pending posts</h3>
                <p class="mt-1 text-sm text-gray-500">
                    All posts in this group have been reviewed. New posts requiring approval will appear here.
                </p>
                <div class="mt-6">
                    <a href="{{ route('groups.show', $group) }}" class="bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700">
                        Back to Group
                    </a>
                </div>
            </div>
        @endif

        <!-- Moderation Guidelines -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">📋 Moderation Guidelines</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
                <div>
                    <h4 class="font-semibold mb-2">✅ Approve posts that:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>Are relevant to the group's purpose</li>
                        <li>Follow community guidelines</li>
                        <li>Contribute positively to discussions</li>
                        <li>Share valuable resources or information</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-2">❌ Reject posts that:</h4>
                    <ul class="list-disc list-inside space-y-1">
                        <li>Contain spam or promotional content</li>
                        <li>Are off-topic or irrelevant</li>
                        <li>Violate community standards</li>
                        <li>Contain inappropriate content</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</x-layouts.unilink-layout>
