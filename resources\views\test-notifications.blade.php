<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Notification Styling Test - UniLink</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">Notification Styling Test</h1>
            
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Test Different Notification Types</h2>
                
                <!-- Test notification dropdown -->
                <div class="relative" x-data="notificationTest()" x-init="init()">
                    <button @click="open = !open" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                        Show Test Notifications
                    </button>
                    
                    <!-- Notification dropdown (same as header) -->
                    <div x-show="open" @click.away="open = false" x-transition 
                         class="absolute left-0 mt-2 w-96 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                        <!-- Header -->
                        <div class="px-4 py-3 border-b border-gray-200">
                            <h3 class="text-sm font-medium text-gray-900">Test Notifications</h3>
                        </div>

                        <!-- Notifications list -->
                        <div class="max-h-96 overflow-y-auto">
                            <template x-for="notification in notifications" :key="notification.id">
                                <div @click="console.log('Clicked:', notification)"
                                     :class="getNotificationClasses(notification)"
                                     class="px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 transition-all duration-200 hover:shadow-sm">
                                    <div class="flex items-start space-x-3">
                                        <!-- Notification Type Icon -->
                                        <div class="flex-shrink-0 relative">
                                            <!-- Avatar -->
                                            <img :src="notification.data.user_avatar || notification.data.group_logo || notification.data.organization_logo || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(notification.data.user_name || notification.data.group_name || notification.data.organization_name || 'User') + '&color=7BC74D&background=EEEEEE&size=64'"
                                                 :alt="notification.data.user_name || notification.data.group_name || notification.data.organization_name"
                                                 class="h-10 w-10 rounded-full object-cover border-2"
                                                 :class="getAvatarBorderClass(notification)"
                                                 onerror="this.src = 'https://ui-avatars.com/api/?name=' + encodeURIComponent(this.alt || 'User') + '&color=7BC74D&background=EEEEEE&size=64'">
                                            
                                            <!-- Type Icon Overlay -->
                                            <div class="absolute -bottom-1 -right-1 rounded-full p-1 shadow-sm"
                                                 :class="getIconBackgroundClass(notification)">
                                                <div x-html="getNotificationIcon(notification)" class="h-3 w-3"></div>
                                            </div>
                                        </div>

                                        <!-- Content -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <p class="text-sm font-medium" 
                                                       :class="getTextClass(notification)"
                                                       x-html="notification.data.message"></p>
                                                    <p class="text-xs text-gray-500 mt-1" x-text="notification.time_ago"></p>
                                                    
                                                    <!-- Additional context for certain notification types -->
                                                    <div x-show="notification.data.reaction_emoji" class="mt-1">
                                                        <span class="text-lg" x-text="notification.data.reaction_emoji"></span>
                                                    </div>
                                                </div>

                                                <!-- Unread indicator -->
                                                <div x-show="!notification.read_at" class="flex-shrink-0 ml-2">
                                                    <div class="h-2 w-2 rounded-full"
                                                         :class="getUnreadIndicatorClass(notification)"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8">
                    <h3 class="text-lg font-semibold mb-4">Notification Categories</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-orange-50 border-l-4 border-orange-200 p-4 rounded">
                            <h4 class="font-medium text-orange-800">Reactions</h4>
                            <p class="text-sm text-orange-600">post_reacted, comment_reacted, share_reacted</p>
                        </div>
                        <div class="bg-blue-50 border-l-4 border-blue-200 p-4 rounded">
                            <h4 class="font-medium text-blue-800">Social</h4>
                            <p class="text-sm text-blue-600">user_followed</p>
                        </div>
                        <div class="bg-green-50 border-l-4 border-green-200 p-4 rounded">
                            <h4 class="font-medium text-green-800">Comments</h4>
                            <p class="text-sm text-green-600">post_commented, comment_replied, share_commented</p>
                        </div>
                        <div class="bg-purple-50 border-l-4 border-purple-200 p-4 rounded">
                            <h4 class="font-medium text-purple-800">Shares</h4>
                            <p class="text-sm text-purple-600">post_shared</p>
                        </div>
                        <div class="bg-indigo-50 border-l-4 border-indigo-200 p-4 rounded">
                            <h4 class="font-medium text-indigo-800">Group Activities</h4>
                            <p class="text-sm text-indigo-600">group_*, membership requests</p>
                        </div>
                        <div class="bg-gray-50 border-l-4 border-gray-200 p-4 rounded">
                            <h4 class="font-medium text-gray-800">Organization Activities</h4>
                            <p class="text-sm text-gray-600">organization_*, requests</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function notificationTest() {
            return {
                open: false,
                notifications: [],

                init() {
                    this.loadTestNotifications();
                },

                loadTestNotifications() {
                    fetch('/test-notifications', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.notifications = data.notifications;
                    })
                    .catch(error => {
                        console.error('Error loading test notifications:', error);
                    });
                },

                // Copy all the styling functions from the header
                getNotificationClasses(notification) {
                    const type = notification.data.type;
                    const isRead = notification.read_at;
                    
                    const baseClasses = isRead ? 'bg-white' : '';
                    const typeClasses = this.getNotificationTypeClasses(type, isRead);
                    
                    return `${baseClasses} ${typeClasses}`;
                },

                getNotificationTypeClasses(type, isRead) {
                    const categories = {
                        // Reactions - Orange theme
                        'post_reacted': isRead ? 'hover:bg-orange-25' : 'bg-orange-50 border-l-4 border-orange-200',
                        'comment_reacted': isRead ? 'hover:bg-orange-25' : 'bg-orange-50 border-l-4 border-orange-200',
                        'share_reacted': isRead ? 'hover:bg-orange-25' : 'bg-orange-50 border-l-4 border-orange-200',
                        
                        // Social - Blue theme
                        'user_followed': isRead ? 'hover:bg-blue-25' : 'bg-blue-50 border-l-4 border-blue-200',
                        
                        // Comments - Green theme
                        'post_commented': isRead ? 'hover:bg-green-25' : 'bg-green-50 border-l-4 border-green-200',
                        'comment_replied': isRead ? 'hover:bg-green-25' : 'bg-green-50 border-l-4 border-green-200',
                        'share_commented': isRead ? 'hover:bg-green-25' : 'bg-green-50 border-l-4 border-green-200',
                        
                        // Shares - Purple theme
                        'post_shared': isRead ? 'hover:bg-purple-25' : 'bg-purple-50 border-l-4 border-purple-200',
                        
                        // Group Activities - Indigo theme
                        'group_member_joined': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                        'group_membership_request': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                        'group_post_created': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                        'group_post_approved': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                        'group_post_rejected': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                        'group_post_pending': isRead ? 'hover:bg-indigo-25' : 'bg-indigo-50 border-l-4 border-indigo-200',
                        
                        // Organization Activities - Gray theme
                        'organization_post_created': isRead ? 'hover:bg-gray-25' : 'bg-gray-50 border-l-4 border-gray-200',
                        'organization_request_submitted': isRead ? 'hover:bg-gray-25' : 'bg-gray-50 border-l-4 border-gray-200',
                        'organization_request_approved': isRead ? 'hover:bg-gray-25' : 'bg-gray-50 border-l-4 border-gray-200',
                        'organization_request_rejected': isRead ? 'hover:bg-gray-25' : 'bg-gray-50 border-l-4 border-gray-200',
                    };
                    
                    return categories[type] || (isRead ? 'hover:bg-gray-50' : 'bg-blue-50');
                },

                getAvatarBorderClass(notification) {
                    const type = notification.data.type;
                    const borderClasses = {
                        'post_reacted': 'border-orange-300', 'comment_reacted': 'border-orange-300', 'share_reacted': 'border-orange-300',
                        'user_followed': 'border-blue-300',
                        'post_commented': 'border-green-300', 'comment_replied': 'border-green-300', 'share_commented': 'border-green-300',
                        'post_shared': 'border-purple-300',
                        'group_member_joined': 'border-indigo-300', 'group_membership_request': 'border-indigo-300', 'group_post_created': 'border-indigo-300', 'group_post_approved': 'border-indigo-300', 'group_post_rejected': 'border-indigo-300', 'group_post_pending': 'border-indigo-300',
                        'organization_post_created': 'border-gray-300', 'organization_request_submitted': 'border-gray-300', 'organization_request_approved': 'border-gray-300', 'organization_request_rejected': 'border-gray-300',
                    };
                    return borderClasses[type] || 'border-gray-300';
                },

                getIconBackgroundClass(notification) {
                    const type = notification.data.type;
                    const bgClasses = {
                        'post_reacted': 'bg-orange-100', 'comment_reacted': 'bg-orange-100', 'share_reacted': 'bg-orange-100',
                        'user_followed': 'bg-blue-100',
                        'post_commented': 'bg-green-100', 'comment_replied': 'bg-green-100', 'share_commented': 'bg-green-100',
                        'post_shared': 'bg-purple-100',
                        'group_member_joined': 'bg-indigo-100', 'group_membership_request': 'bg-indigo-100', 'group_post_created': 'bg-indigo-100', 'group_post_approved': 'bg-indigo-100', 'group_post_rejected': 'bg-indigo-100', 'group_post_pending': 'bg-indigo-100',
                        'organization_post_created': 'bg-gray-100', 'organization_request_submitted': 'bg-gray-100', 'organization_request_approved': 'bg-gray-100', 'organization_request_rejected': 'bg-gray-100',
                    };
                    return bgClasses[type] || 'bg-gray-100';
                },

                getTextClass(notification) {
                    const type = notification.data.type;
                    const textClasses = {
                        'post_reacted': 'text-orange-800', 'comment_reacted': 'text-orange-800', 'share_reacted': 'text-orange-800',
                        'user_followed': 'text-blue-800',
                        'post_commented': 'text-green-800', 'comment_replied': 'text-green-800', 'share_commented': 'text-green-800',
                        'post_shared': 'text-purple-800',
                        'group_member_joined': 'text-indigo-800', 'group_membership_request': 'text-indigo-800', 'group_post_created': 'text-indigo-800', 'group_post_approved': 'text-indigo-800', 'group_post_rejected': 'text-indigo-800', 'group_post_pending': 'text-indigo-800',
                        'organization_post_created': 'text-gray-800', 'organization_request_submitted': 'text-gray-800', 'organization_request_approved': 'text-gray-800', 'organization_request_rejected': 'text-gray-800',
                    };
                    return textClasses[type] || 'text-gray-800';
                },

                getUnreadIndicatorClass(notification) {
                    const type = notification.data.type;
                    const indicatorClasses = {
                        'post_reacted': 'bg-orange-500', 'comment_reacted': 'bg-orange-500', 'share_reacted': 'bg-orange-500',
                        'user_followed': 'bg-blue-500',
                        'post_commented': 'bg-green-500', 'comment_replied': 'bg-green-500', 'share_commented': 'bg-green-500',
                        'post_shared': 'bg-purple-500',
                        'group_member_joined': 'bg-indigo-500', 'group_membership_request': 'bg-indigo-500', 'group_post_created': 'bg-indigo-500', 'group_post_approved': 'bg-indigo-500', 'group_post_rejected': 'bg-indigo-500', 'group_post_pending': 'bg-indigo-500',
                        'organization_post_created': 'bg-gray-500', 'organization_request_submitted': 'bg-gray-500', 'organization_request_approved': 'bg-gray-500', 'organization_request_rejected': 'bg-gray-500',
                    };
                    return indicatorClasses[type] || 'bg-blue-500';
                },

                getNotificationIcon(notification) {
                    const type = notification.data.type;
                    const icons = {
                        'post_reacted': '<svg class="text-orange-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/></svg>',
                        'user_followed': '<svg class="text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/></svg>',
                        'post_commented': '<svg class="text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"/></svg>',
                        'group_post_approved': '<svg class="text-indigo-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/></svg>',
                        'organization_request_approved': '<svg class="text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm7 4a1 1 0 10-2 0v3.586L9.707 8.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L13 9.586V6z" clip-rule="evenodd"/></svg>',
                    };
                    return icons[type] || '<svg class="text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/></svg>';
                }
            }
        }
    </script>
</body>
</html>
