<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Post;
use App\Models\Tag;
use App\Models\PostMethod;
use App\Models\Organization;
use App\Models\Group;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ComprehensiveFilterTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $userPostMethod;
    protected $orgPostMethod;
    protected $groupPostMethod;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        // Create post methods
        $this->userPostMethod = PostMethod::firstOrCreate([
            'slug' => 'user'
        ], [
            'name' => 'User',
            'description' => 'Posts created by individual users',
            'is_active' => true
        ]);

        $this->orgPostMethod = PostMethod::firstOrCreate([
            'slug' => 'organization'
        ], [
            'name' => 'Organization',
            'description' => 'Posts created by organizations',
            'is_active' => true
        ]);

        $this->groupPostMethod = PostMethod::firstOrCreate([
            'slug' => 'group'
        ], [
            'name' => 'Group',
            'description' => 'Posts created within groups',
            'is_active' => true
        ]);
    }

    public function test_search_filter_works()
    {
        // Create posts with different content
        $post1 = Post::factory()->create([
            'title' => 'Laravel Tutorial',
            'content' => 'Learn Laravel framework',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $post2 = Post::factory()->create([
            'title' => 'PHP Basics',
            'content' => 'Introduction to PHP programming',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?search=Laravel');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Laravel Tutorial', $response->json('html'));
        $this->assertStringNotContainsString('PHP Basics', $response->json('html'));
    }

    public function test_post_type_filter_works()
    {
        $organization = Organization::factory()->create();
        
        // Create user post
        $userPost = Post::factory()->create([
            'title' => 'User Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'organization_id' => null,
            'group_id' => null,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Create organization post
        $orgPost = Post::factory()->create([
            'title' => 'Organization Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->orgPostMethod->id,
            'organization_id' => $organization->id,
            'group_id' => null,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Test user posts filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?post_type=user');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('User Post', $response->json('html'));
        $this->assertStringNotContainsString('Organization Post', $response->json('html'));

        // Test organization posts filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?post_type=organization');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Organization Post', $response->json('html'));
        $this->assertStringNotContainsString('User Post', $response->json('html'));
    }

    public function test_tags_filter_works()
    {
        $tag1 = Tag::firstOrCreate([
            'name' => 'Technology',
            'post_method_id' => $this->userPostMethod->id
        ], [
            'slug' => 'technology',
            'color' => '#3B82F6',
            'is_active' => true
        ]);

        $tag2 = Tag::firstOrCreate([
            'name' => 'Education',
            'post_method_id' => $this->userPostMethod->id
        ], [
            'slug' => 'education',
            'color' => '#10B981',
            'is_active' => true
        ]);

        $post1 = Post::factory()->create([
            'title' => 'Tech Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $post2 = Post::factory()->create([
            'title' => 'Education Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $post1->tags()->attach($tag1->id);
        $post2->tags()->attach($tag2->id);

        // Test single tag filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?tags[]=' . $tag1->id);

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Tech Post', $response->json('html'));
        $this->assertStringNotContainsString('Education Post', $response->json('html'));

        // Test multiple tags filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?tags[]=' . $tag1->id . '&tags[]=' . $tag2->id);

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $html = $response->json('html');
        $this->assertStringContainsString('Tech Post', $html);
        $this->assertStringContainsString('Education Post', $html);
    }

    public function test_date_range_filter_works()
    {
        // Create posts with different dates
        $todayPost = Post::factory()->create([
            'title' => 'Today Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $oldPost = Post::factory()->create([
            'title' => 'Old Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()->subWeek()
        ]);

        // Test today filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?date_range=today');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Today Post', $response->json('html'));
        $this->assertStringNotContainsString('Old Post', $response->json('html'));

        // Test week filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?date_range=week');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $html = $response->json('html');
        $this->assertStringContainsString('Today Post', $html);
    }

    public function test_privacy_filter_works()
    {
        $organization = Organization::factory()->create();
        
        // Create public post (user post)
        $publicPost = Post::factory()->create([
            'title' => 'Public Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'organization_id' => null,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Create org members post
        $orgPost = Post::factory()->create([
            'title' => 'Org Post',
            'user_id' => $this->user->id,
            'post_method_id' => $this->orgPostMethod->id,
            'organization_id' => $organization->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Test public filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?privacy=public');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Public Post', $response->json('html'));
        $this->assertStringNotContainsString('Org Post', $response->json('html'));

        // Test org members filter
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?privacy=org_members');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Org Post', $response->json('html'));
        $this->assertStringNotContainsString('Public Post', $response->json('html'));
    }

    public function test_combined_filters_work()
    {
        $tag = Tag::firstOrCreate([
            'name' => 'Event',
            'post_method_id' => $this->userPostMethod->id
        ], [
            'slug' => 'event',
            'color' => '#10B981',
            'is_active' => true
        ]);

        $post = Post::factory()->create([
            'title' => 'Laravel Conference Today',
            'user_id' => $this->user->id,
            'post_method_id' => $this->userPostMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $post->tags()->attach($tag->id);

        // Test multiple filters combined
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?search=Laravel&post_type=user&tags[]=' . $tag->id . '&date_range=today');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertStringContainsString('Laravel Conference Today', $response->json('html'));
    }

    public function test_tags_api_endpoint_works()
    {
        $tag = Tag::firstOrCreate([
            'name' => 'Test Tag',
            'post_method_id' => $this->userPostMethod->id
        ], [
            'slug' => 'test-tag',
            'color' => '#3B82F6',
            'is_active' => true
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/tags');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'tags' => [
                    '*' => ['id', 'name', 'slug', 'color', 'post_method_id']
                ]
            ]);

        $this->assertStringContainsString('Test Tag', $response->getContent());
    }
}
