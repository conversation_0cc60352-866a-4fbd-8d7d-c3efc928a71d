<div class="flex items-center space-x-4">
    @auth
        <button
            wire:click="toggleFollow"
            wire:loading.attr="disabled"
            class="flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 {{ $isFollowing ? 'bg-gray-600 text-white hover:bg-gray-700' : 'bg-blue-600 text-white hover:bg-blue-700' }}">

            @if($isFollowing)
                @if(in_array($userRole, ['president', 'vice_president', 'secretary', 'treasurer', 'officer']))
                    <span wire:loading.remove wire:target="toggleFollow">Manage</span>
                @else
                    <span wire:loading.remove wire:target="toggleFollow">Joined</span>
                @endif
            @else
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" wire:loading.remove wire:target="toggleFollow">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span wire:loading.remove wire:target="toggleFollow">Join</span>
            @endif

            <span wire:loading wire:target="toggleFollow">
                <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                @if($isFollowing)
                    @if(in_array($userRole, ['president', 'vice_president', 'secretary', 'treasurer', 'officer']))
                        Opening...
                    @else
                        Leaving...
                    @endif
                @else
                    Joining...
                @endif
            </span>
        </button>
    @else
        <a href="{{ route('login') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 flex items-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>Join</span>
        </a>
    @endauth


</div>
