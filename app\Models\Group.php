<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Group extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'cover_image',
        'visibility',
        'post_approval',
        'allow_file_sharing',
        'allowed_file_types',
        'max_file_size_mb',
        'status',
        'created_by',
        'organization_id',
        'school_id',
        'campus_id',
    ];

    protected $casts = [
        'allowed_file_types' => 'array',
        'allow_file_sharing' => 'boolean',
    ];

    /**
     * Get the user who created this group
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the organization this group belongs to (optional)
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the school this group belongs to
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the campus this group belongs to
     */
    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    /**
     * Get all members of this group
     */
    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'group_members')
            ->using(GroupMember::class)
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps();
    }

    /**
     * Get active members only
     */
    public function activeMembers(): BelongsToMany
    {
        return $this->members()->wherePivot('status', 'active');
    }

    /**
     * Get pending members
     */
    public function pendingMembers(): BelongsToMany
    {
        return $this->members()->wherePivot('status', 'pending');
    }

    /**
     * Get group admins and moderators
     */
    public function moderators(): BelongsToMany
    {
        return $this->members()->whereIn('group_members.role', ['admin', 'moderator']);
    }

    /**
     * Get posts in this group
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    /**
     * Get approved posts in this group
     */
    public function approvedPosts(): HasMany
    {
        return $this->posts()->where('approval_status', 'approved');
    }

    /**
     * Get pending posts awaiting approval
     */
    public function pendingPosts(): HasMany
    {
        return $this->posts()->where('approval_status', 'pending');
    }

    /**
     * Check if user is a member of this group
     */
    public function hasMember(User $user): bool
    {
        return $this->members()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if user is an active member
     */
    public function hasActiveMember(User $user): bool
    {
        return $this->activeMembers()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if user can post in this group
     */
    public function userCanPost(User $user): bool
    {
        return $this->hasActiveMember($user);
    }

    /**
     * Check if user can moderate this group
     */
    public function userCanModerate(?User $user): bool
    {
        if (!$user) {
            return false;
        }

        if ($user->isAdmin() || $this->created_by === $user->id) {
            return true;
        }

        $membership = $this->members()->where('user_id', $user->id)->first();
        return $membership && in_array($membership->pivot->role, ['admin', 'moderator']);
    }

    /**
     * Scope for public groups
     */
    public function scopePublic($query)
    {
        return $query->where('visibility', 'public');
    }

    /**
     * Scope for private groups
     */
    public function scopePrivate($query)
    {
        return $query->where('visibility', 'private');
    }

    /**
     * Scope for active groups
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Generate unique slug from name
     */
    public static function generateSlug(string $name): string
    {
        $slug = Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the route key for the model
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
