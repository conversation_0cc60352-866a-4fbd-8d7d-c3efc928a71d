<?php

namespace App\Livewire;

use App\Models\Group;
use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class GroupMembership extends Component
{
    public Group $group;
    public $userMembership = null;
    public $isLoading = false;

    public function mount(Group $group)
    {
        $this->group = $group;
        $this->loadUserMembership();
    }

    public function loadUserMembership()
    {
        if (Auth::check()) {
            $this->userMembership = $this->group->members()
                ->where('user_id', Auth::id())
                ->first();
        }
    }

    public function joinGroup()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->isLoading = true;

        try {
            $user = Auth::user();

            // Check if already a member
            if ($this->group->hasMember($user)) {
                session()->flash('error', 'You are already a member of this group.');
                return;
            }

            // For private groups, join as pending; for public groups, join as active
            $status = $this->group->visibility === 'private' ? 'pending' : 'active';

            $this->group->members()->attach($user->id, [
                'role' => 'member',
                'status' => $status,
                'joined_at' => $status === 'active' ? now() : null,
            ]);

            // Send notifications
            if ($status === 'pending') {
                // Notify group admins/moderators about membership request
                $this->group->moderators()->each(function ($admin) use ($user) {
                    $admin->notify(new \App\Notifications\GroupMembershipRequest($user, $this->group));
                });
            } else {
                // Notify group creator about new member joining
                if ($this->group->creator && $this->group->creator->id !== $user->id) {
                    $this->group->creator->notify(new \App\Notifications\GroupMemberJoined($user, $this->group));
                }
            }

            $message = $status === 'pending'
                ? 'Join request sent! Waiting for approval.'
                : 'Successfully joined the group!';

            session()->flash('success', $message);
            $this->loadUserMembership();

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while joining the group.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function leaveGroup()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->isLoading = true;

        try {
            $user = Auth::user();

            // Check if user is a member
            if (!$this->group->hasMember($user)) {
                session()->flash('error', 'You are not a member of this group.');
                return;
            }

            // Prevent group creator from leaving
            if ($this->group->created_by === $user->id) {
                session()->flash('error', 'Group creators cannot leave their own group.');
                return;
            }

            $this->group->members()->detach($user->id);

            session()->flash('success', 'You have left the group.');
            $this->userMembership = null;

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while leaving the group.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function cancelRequest()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->isLoading = true;

        try {
            $user = Auth::user();

            // Check if user has a pending request
            $membership = $this->group->members()
                ->where('user_id', $user->id)
                ->wherePivot('status', 'pending')
                ->first();

            if (!$membership) {
                session()->flash('error', 'You do not have a pending request for this group.');
                return;
            }

            $this->group->members()->detach($user->id);

            session()->flash('success', 'Join request cancelled.');
            $this->userMembership = null;

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while cancelling the request.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.group-membership');
    }
}
