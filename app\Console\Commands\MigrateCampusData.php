<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\School;
use App\Models\Campus;
use Illuminate\Console\Command;

class MigrateCampusData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:campus-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing campus text data to school/campus relationships';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting campus data migration...');

        // Get the default school (SKSU)
        $defaultSchool = School::first();

        if (!$defaultSchool) {
            $this->error('No schools found. Please run the SchoolCampusSeeder first.');
            return 1;
        }

        // Get users with old campus text data but no school/campus relationships
        $users = User::whereNotNull('campus')
            ->where('campus', '!=', '')
            ->whereNull('school_id')
            ->whereNull('campus_id')
            ->get();

        $this->info("Found {$users->count()} users with old campus data to migrate.");

        $migratedCount = 0;
        $skippedCount = 0;

        foreach ($users as $user) {
            // Try to match the campus text to an existing campus
            $campus = Campus::where('school_id', $defaultSchool->id)
                ->where(function ($query) use ($user) {
                    $query->where('name', 'like', '%' . $user->campus . '%')
                          ->orWhere('slug', 'like', '%' . str_replace(' ', '-', strtolower($user->campus)) . '%');
                })
                ->first();

            if ($campus) {
                $user->update([
                    'school_id' => $defaultSchool->id,
                    'campus_id' => $campus->id,
                ]);
                $migratedCount++;
                $this->line("✓ Migrated user {$user->name}: '{$user->campus}' → {$campus->name}");
            } else {
                // Set to default school and main campus if no match found
                $mainCampus = Campus::where('school_id', $defaultSchool->id)
                    ->where('is_main_campus', true)
                    ->first();

                if ($mainCampus) {
                    $user->update([
                        'school_id' => $defaultSchool->id,
                        'campus_id' => $mainCampus->id,
                    ]);
                    $migratedCount++;
                    $this->line("? Migrated user {$user->name}: '{$user->campus}' → {$mainCampus->name} (default)");
                } else {
                    $skippedCount++;
                    $this->warn("✗ Skipped user {$user->name}: '{$user->campus}' (no matching campus found)");
                }
            }
        }

        $this->info("Migration completed!");
        $this->info("Migrated: {$migratedCount} users");
        $this->info("Skipped: {$skippedCount} users");

        return 0;
    }
}
