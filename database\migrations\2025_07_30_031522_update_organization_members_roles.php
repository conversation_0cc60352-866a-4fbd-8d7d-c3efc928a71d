<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organization_members', function (Blueprint $table) {
            // Update the role enum to include specific officer positions
            $table->enum('role', [
                'member',
                'officer',
                'secretary',
                'treasurer',
                'vice_president',
                'president'
            ])->default('member')->change();

            // Add a custom role title field for flexibility
            $table->string('custom_role_title')->nullable()->after('role');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organization_members', function (Blueprint $table) {
            // Revert back to original role enum
            $table->enum('role', ['member', 'officer', 'president'])->default('member')->change();

            // Remove custom role title field
            $table->dropColumn('custom_role_title');
        });
    }
};
