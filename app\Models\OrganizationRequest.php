<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrganizationRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'organization_name',
        'description',
        'proof_document',
        'status',
        'admin_notes',
        'reviewed_by',
        'reviewed_at',
        'organization_created',
        'created_organization_id',
    ];

    protected $casts = [
        'reviewed_at' => 'datetime',
        'organization_created' => 'boolean',
    ];

    /**
     * Get the user who submitted this request
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin who reviewed this request
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the organization created from this request
     */
    public function createdOrganization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'created_organization_id');
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved requests
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected requests
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Check if the request is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the request is approved
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the request is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if an organization has been created from this request
     */
    public function hasCreatedOrganization(): bool
    {
        return $this->organization_created && $this->created_organization_id !== null;
    }

    /**
     * Mark the request as having created an organization
     */
    public function markOrganizationCreated(Organization $organization): void
    {
        $this->update([
            'organization_created' => true,
            'created_organization_id' => $organization->id,
        ]);
    }
}
