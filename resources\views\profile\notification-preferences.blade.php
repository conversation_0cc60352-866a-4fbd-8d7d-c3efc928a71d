<x-layouts.unilink-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Notification Preferences') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('notification-preferences.update') }}" x-data="notificationPreferences()">
                        @csrf
                        @method('PATCH')

                        <!-- Master Toggle -->
                        <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Enable Notifications</h3>
                                    <p class="text-sm text-gray-600">Turn all notifications on or off</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" 
                                           name="notifications_enabled" 
                                           value="1"
                                           x-model="notificationsEnabled"
                                           {{ auth()->user()->notifications_enabled ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-custom-green/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-custom-green"></div>
                                </label>
                            </div>
                        </div>

                        <!-- Notification Categories -->
                        <div x-show="notificationsEnabled" x-transition class="space-y-6">
                            
                            <!-- Post Interactions -->
                            <div class="border-b border-gray-200 pb-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Post Interactions</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Post Reactions</label>
                                            <p class="text-sm text-gray-500">When someone reacts to your posts</p>
                                        </div>
                                        <input type="checkbox" name="post_reactions" value="1" 
                                               {{ $preferences['post_reactions'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Post Comments</label>
                                            <p class="text-sm text-gray-500">When someone comments on your posts</p>
                                        </div>
                                        <input type="checkbox" name="post_comments" value="1" 
                                               {{ $preferences['post_comments'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Post Shares</label>
                                            <p class="text-sm text-gray-500">When someone shares your posts</p>
                                        </div>
                                        <input type="checkbox" name="post_shares" value="1" 
                                               {{ $preferences['post_shares'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Comment Replies</label>
                                            <p class="text-sm text-gray-500">When someone replies to your comments</p>
                                        </div>
                                        <input type="checkbox" name="comment_replies" value="1" 
                                               {{ $preferences['comment_replies'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                </div>
                            </div>

                            <!-- Social -->
                            <div class="border-b border-gray-200 pb-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Social</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">User Follows</label>
                                            <p class="text-sm text-gray-500">When someone follows you</p>
                                        </div>
                                        <input type="checkbox" name="user_follows" value="1" 
                                               {{ $preferences['user_follows'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                </div>
                            </div>

                            <!-- Organizations & Groups -->
                            <div class="border-b border-gray-200 pb-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Organizations & Groups</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Organization Posts</label>
                                            <p class="text-sm text-gray-500">New posts from organizations you follow</p>
                                        </div>
                                        <input type="checkbox" name="organization_posts" value="1" 
                                               {{ $preferences['organization_posts'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Group Posts</label>
                                            <p class="text-sm text-gray-500">New posts in groups you're a member of</p>
                                        </div>
                                        <input type="checkbox" name="group_posts" value="1" 
                                               {{ $preferences['group_posts'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Group Memberships</label>
                                            <p class="text-sm text-gray-500">Group membership requests and approvals</p>
                                        </div>
                                        <input type="checkbox" name="group_memberships" value="1" 
                                               {{ $preferences['group_memberships'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                </div>
                            </div>

                            <!-- System -->
                            <div class="pb-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">System</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Scholarship Updates</label>
                                            <p class="text-sm text-gray-500">New scholarships and updates</p>
                                        </div>
                                        <input type="checkbox" name="scholarship_updates" value="1" 
                                               {{ $preferences['scholarship_updates'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-gray-700">Admin Notifications</label>
                                            <p class="text-sm text-gray-500">Important system announcements</p>
                                        </div>
                                        <input type="checkbox" name="admin_notifications" value="1" 
                                               {{ $preferences['admin_notifications'] ? 'checked' : '' }}
                                               class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Save Button -->
                        <div class="flex items-center justify-end mt-8">
                            <button type="submit" 
                                    class="bg-custom-green hover:bg-custom-green/90 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                Save Preferences
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
    function notificationPreferences() {
        return {
            notificationsEnabled: {{ auth()->user()->notifications_enabled ? 'true' : 'false' }}
        }
    }
    </script>
</x-layouts.unilink-layout>
