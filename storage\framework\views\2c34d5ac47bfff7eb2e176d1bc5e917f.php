<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Manage Members</h1>
                    <p class="text-gray-600 mt-1"><?php echo e($group->name); ?></p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('groups.show', $group)); ?>" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        Back to Group
                    </a>
                    <a href="<?php echo e(route('groups.edit', $group)); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                        Edit Group
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Members</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e(is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($group->pendingMembers->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Moderators</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($group->moderators->count()); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Members</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($group->members->count()); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Members -->
        <?php if($group->pendingMembers->count() > 0): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Pending Approval (<?php echo e($group->pendingMembers->count()); ?>)
                    </h2>
                </div>
                
                <div class="divide-y divide-gray-200">
                    <?php $__currentLoopData = $group->pendingMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <img class="h-10 w-10 rounded-full" src="<?php echo e($member->avatar ? Storage::disk('public')->url($member->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($member->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e($member->name); ?>">
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900"><?php echo e($member->name); ?></h3>
                                        <p class="text-sm text-gray-500"><?php echo e($member->email); ?></p>
                                        <p class="text-xs text-gray-400">Requested <?php echo e($member->pivot->created_at->diffForHumans()); ?></p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <form action="<?php echo e(route('groups.approve-member', [$group, $member])); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="bg-green-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-green-700">
                                            Approve
                                        </button>
                                    </form>
                                    <form action="<?php echo e(route('groups.reject-member', [$group, $member])); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-red-700" onclick="return confirm('Are you sure you want to reject this member request?')">
                                            Reject
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Active Members -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Active Members (<?php echo e(is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count()); ?>)
                </h2>
            </div>
            
            <div class="divide-y divide-gray-200">
                <?php $__currentLoopData = (is_array($group->activeMembers) ? collect($group->activeMembers) : $group->activeMembers); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <img class="h-10 w-10 rounded-full" src="<?php echo e($member->avatar ? Storage::disk('public')->url($member->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($member->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e($member->name); ?>">
                                <div>
                                    <div class="flex items-center space-x-2">
                                        <h3 class="text-sm font-medium text-gray-900"><?php echo e($member->name); ?></h3>
                                        <?php if($member->id === $group->created_by): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                                Creator
                                            </span>
                                        <?php elseif($member->pivot->role === 'admin'): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                                Admin
                                            </span>
                                        <?php elseif($member->pivot->role === 'moderator'): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                Moderator
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                                Member
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="text-sm text-gray-500"><?php echo e($member->email); ?></p>
                                    <p class="text-xs text-gray-400">Joined <?php echo e($member->pivot->joined_at ? $member->pivot->joined_at->diffForHumans() : 'recently'); ?></p>
                                </div>
                            </div>
                            
                            <?php if($member->id !== $group->created_by): ?>
                                <div class="flex items-center space-x-2">
                                    <!-- Role Change -->
                                    <form action="<?php echo e(route('groups.update-member-role', [$group, $member])); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('PATCH'); ?>
                                        <select name="role" onchange="this.form.submit()" class="text-sm border-gray-300 rounded-md">
                                            <option value="member" <?php echo e($member->pivot->role === 'member' ? 'selected' : ''); ?>>Member</option>
                                            <option value="moderator" <?php echo e($member->pivot->role === 'moderator' ? 'selected' : ''); ?>>Moderator</option>
                                            <option value="admin" <?php echo e($member->pivot->role === 'admin' ? 'selected' : ''); ?>>Admin</option>
                                        </select>
                                    </form>
                                    
                                    <!-- Remove Member -->
                                    <form action="<?php echo e(route('groups.remove-member', [$group, $member])); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="text-red-600 hover:text-red-800 text-sm font-medium" onclick="return confirm('Are you sure you want to remove this member?')">
                                            Remove
                                        </button>
                                    </form>
                                </div>
                            <?php else: ?>
                                <span class="text-sm text-gray-500 italic">Group Creator</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Group Settings Summary -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Group Settings</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                <div>
                    <p class="font-medium text-gray-700">Visibility</p>
                    <p class="text-gray-600"><?php echo e(ucfirst($group->visibility)); ?></p>
                </div>
                <div>
                    <p class="font-medium text-gray-700">Post Approval</p>
                    <p class="text-gray-600"><?php echo e($group->post_approval === 'none' ? 'Not Required' : 'Required'); ?></p>
                </div>
                <div>
                    <p class="font-medium text-gray-700">File Sharing</p>
                    <p class="text-gray-600"><?php echo e($group->allow_file_sharing ? 'Enabled' : 'Disabled'); ?></p>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/groups/members.blade.php ENDPATH**/ ?>