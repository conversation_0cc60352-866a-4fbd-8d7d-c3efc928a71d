<?php

namespace App\Livewire;

use App\Models\Organization;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class OrganizationFollower extends Component
{
    public Organization $organization;
    public $isFollowing = false;
    public $followersCount = 0;
    public $isLoading = false;
    public $userRole = null;

    public function mount(Organization $organization)
    {
        $this->organization = $organization;
        $this->loadFollowStatus();
        $this->loadFollowersCount();
    }

    public function loadFollowStatus()
    {
        if (Auth::check()) {
            // Check if user is a member of this organization
            $membership = $this->organization->members()->where('user_id', Auth::id())->where('status', 'active')->first();
            $this->isFollowing = $membership !== null;
            $this->userRole = $membership ? $membership->pivot->role : null;
        }
    }

    public function loadFollowersCount()
    {
        $this->followersCount = $this->organization->activeMembers()->count();
    }

    public function toggleFollow()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->isLoading = true;

        try {
            $user = Auth::user();

            if ($this->isFollowing) {
                // Check if user is an officer - officers should manage through organization settings
                if (in_array($this->userRole, ['president', 'vice_president', 'secretary', 'treasurer', 'officer'])) {
                    // Redirect to organization management instead of leaving
                    return redirect()->route('organizations.show', $this->organization);
                }

                // Leave organization (only for regular members)
                $this->organization->members()->detach($user->id);
                $this->isFollowing = false;
                $this->userRole = null;
                $this->followersCount--;
                session()->flash('success', 'You have left this organization.');
            } else {
                // Join organization
                $this->organization->members()->attach($user->id, [
                    'role' => 'member',
                    'status' => 'active',
                    'joined_at' => now(),
                ]);
                $this->isFollowing = true;
                $this->userRole = 'member';
                $this->followersCount++;
                session()->flash('success', 'You have successfully joined this organization!');
            }

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while updating your membership status.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.organization-follower');
    }
}
