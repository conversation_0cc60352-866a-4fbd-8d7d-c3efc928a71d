<?php

namespace Tests\Feature;

use App\Models\Organization;
use App\Models\OrganizationRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class OrganizationRequestWorkflowTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_can_submit_organization_request()
    {
        Storage::fake('public');
        
        $user = User::factory()->create();
        $proofFile = UploadedFile::fake()->create('proof.pdf', 1000);

        $response = $this->actingAs($user)->post('/organization-requests', [
            'organization_name' => 'Test Student Organization',
            'description' => 'This is a test organization for students to collaborate and learn together. We focus on academic excellence and community service.',
            'proof_document' => $proofFile,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('organization_requests', [
            'user_id' => $user->id,
            'organization_name' => 'Test Student Organization',
            'status' => 'pending',
        ]);

        Storage::disk('public')->assertExists('organization-requests/proofs/' . $proofFile->hashName());
    }

    /** @test */
    public function user_cannot_submit_duplicate_pending_request()
    {
        $user = User::factory()->create();
        
        // Create existing pending request
        OrganizationRequest::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($user)->post('/organization-requests', [
            'organization_name' => 'Another Organization',
            'description' => 'This should fail because user already has pending request.',
            'proof_document' => UploadedFile::fake()->create('proof.pdf', 1000),
        ]);

        $response->assertSessionHasErrors();
    }

    /** @test */
    public function admin_can_approve_organization_request()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create();
        
        $request = OrganizationRequest::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($admin)->post("/admin/organization-requests/{$request->id}/approve", [
            'admin_notes' => 'Request approved. Good documentation provided.',
        ]);

        $response->assertRedirect();
        $request->refresh();
        
        $this->assertEquals('approved', $request->status);
        $this->assertEquals($admin->id, $request->reviewed_by);
        $this->assertNotNull($request->reviewed_at);
    }

    /** @test */
    public function admin_can_reject_organization_request()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create();
        
        $request = OrganizationRequest::factory()->create([
            'user_id' => $user->id,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($admin)->post("/admin/organization-requests/{$request->id}/reject", [
            'admin_notes' => 'Insufficient documentation provided.',
        ]);

        $response->assertRedirect();
        $request->refresh();
        
        $this->assertEquals('rejected', $request->status);
        $this->assertEquals($admin->id, $request->reviewed_by);
        $this->assertNotNull($request->reviewed_at);
    }

    /** @test */
    public function user_can_create_organization_from_approved_request()
    {
        $user = User::factory()->create();
        
        $approvedRequest = OrganizationRequest::factory()->create([
            'user_id' => $user->id,
            'organization_name' => 'Approved Organization',
            'status' => 'approved',
            'organization_created' => false,
        ]);

        $response = $this->actingAs($user)->post('/organizations', [
            'name' => 'Approved Organization',
            'description' => 'This organization was created from an approved request.',
            'email' => '<EMAIL>',
        ]);

        $response->assertRedirect();
        
        $this->assertDatabaseHas('organizations', [
            'name' => 'Approved Organization',
            'created_by' => $user->id,
            'status' => 'active',
        ]);

        $approvedRequest->refresh();
        $this->assertTrue($approvedRequest->organization_created);
        $this->assertNotNull($approvedRequest->created_organization_id);
    }

    /** @test */
    public function user_cannot_create_organization_without_approved_request()
    {
        $user = User::factory()->create(); // Regular user, not admin

        $response = $this->actingAs($user)->post('/organizations', [
            'name' => 'Unauthorized Organization',
            'description' => 'This should fail.',
            'email' => '<EMAIL>',
        ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors();
        
        $this->assertDatabaseMissing('organizations', [
            'name' => 'Unauthorized Organization',
        ]);
    }

    /** @test */
    public function president_can_manage_officers()
    {
        $president = User::factory()->create();
        $member = User::factory()->create();
        
        $organization = Organization::factory()->create(['created_by' => $president->id]);
        
        // Add president and member
        $organization->members()->attach($president->id, [
            'role' => 'president',
            'status' => 'active',
            'joined_at' => now(),
        ]);
        
        $organization->members()->attach($member->id, [
            'role' => 'member',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        // President appoints member as secretary
        $response = $this->actingAs($president)->post("/organizations/{$organization->id}/officers", [
            'user_id' => $member->id,
            'role' => 'secretary',
            'custom_role_title' => 'Executive Secretary',
        ]);

        $response->assertRedirect();
        
        $membership = $organization->members()->where('user_id', $member->id)->first();
        $this->assertEquals('secretary', $membership->pivot->role);
        $this->assertEquals('Executive Secretary', $membership->pivot->custom_role_title);
    }

    /** @test */
    public function officers_can_access_organization_dashboard()
    {
        $officer = User::factory()->create();
        $organization = Organization::factory()->create();
        
        $organization->members()->attach($officer->id, [
            'role' => 'secretary',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        $response = $this->actingAs($officer)->get("/organizations/{$organization->id}/dashboard");
        
        $response->assertOk();
        $response->assertViewIs('organizations.dashboard');
    }

    /** @test */
    public function regular_members_cannot_access_organization_dashboard()
    {
        $member = User::factory()->create();
        $organization = Organization::factory()->create();
        
        $organization->members()->attach($member->id, [
            'role' => 'member',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        $response = $this->actingAs($member)->get("/organizations/{$organization->id}/dashboard");
        
        $response->assertForbidden();
    }
}
