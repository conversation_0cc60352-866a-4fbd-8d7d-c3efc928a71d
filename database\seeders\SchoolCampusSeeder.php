<?php

namespace Database\Seeders;

use App\Models\School;
use App\Models\Campus;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class SchoolCampusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Sultan Kudarat State University
        $sksu = School::create([
            'name' => 'Sultan Kudarat State University',
            'slug' => 'sultan-kudarat-state-university',
            'abbreviation' => 'SKSU',
            'description' => 'Sultan Kudarat State University is a state university in the Philippines. It was established in 1993 and is located in the province of Sultan Kudarat.',
            'website' => 'https://sksu.edu.ph',
            'address' => 'EJC Montilla, Tacurong City, Sultan Kudarat, Philippines',
            'contact_info' => [
                'phone' => '+63 64 477 8442',
                'email' => '<EMAIL>',
                'fax' => '+63 64 477 8442'
            ],
            'status' => 'active',
        ]);

        // Create SKSU Campuses
        $campuses = [
            [
                'name' => 'ACCESS Campus (Main Campus)',
                'slug' => 'access-campus',
                'description' => 'The main campus of Sultan Kudarat State University located in Tacurong City.',
                'address' => 'EJC Montilla, Tacurong City, Sultan Kudarat, Philippines',
                'is_main_campus' => true,
            ],
            [
                'name' => 'Isulan Campus',
                'slug' => 'isulan-campus',
                'description' => 'SKSU Isulan Campus located in Isulan, Sultan Kudarat.',
                'address' => 'Isulan, Sultan Kudarat, Philippines',
                'is_main_campus' => false,
            ],
            [
                'name' => 'Bagumbayan Campus',
                'slug' => 'bagumbayan-campus',
                'description' => 'SKSU Bagumbayan Campus located in Bagumbayan, Sultan Kudarat.',
                'address' => 'Bagumbayan, Sultan Kudarat, Philippines',
                'is_main_campus' => false,
            ],
            [
                'name' => 'Lutayan Campus',
                'slug' => 'lutayan-campus',
                'description' => 'SKSU Lutayan Campus located in Lutayan, Sultan Kudarat.',
                'address' => 'Lutayan, Sultan Kudarat, Philippines',
                'is_main_campus' => false,
            ],
            [
                'name' => 'Kalamansig Campus',
                'slug' => 'kalamansig-campus',
                'description' => 'SKSU Kalamansig Campus located in Kalamansig, Sultan Kudarat.',
                'address' => 'Kalamansig, Sultan Kudarat, Philippines',
                'is_main_campus' => false,
            ],
            [
                'name' => 'Palimbang Campus',
                'slug' => 'palimbang-campus',
                'description' => 'SKSU Palimbang Campus located in Palimbang, Sultan Kudarat.',
                'address' => 'Palimbang, Sultan Kudarat, Philippines',
                'is_main_campus' => false,
            ],
            [
                'name' => 'Tacurong Campus',
                'slug' => 'tacurong-campus',
                'description' => 'SKSU Tacurong Campus located in Tacurong City, Sultan Kudarat.',
                'address' => 'Tacurong City, Sultan Kudarat, Philippines',
                'is_main_campus' => false,
            ],
        ];

        foreach ($campuses as $campusData) {
            Campus::create([
                'school_id' => $sksu->id,
                'name' => $campusData['name'],
                'slug' => $campusData['slug'],
                'description' => $campusData['description'],
                'address' => $campusData['address'],
                'is_main_campus' => $campusData['is_main_campus'],
                'contact_info' => [
                    'phone' => '+63 64 477 8442',
                    'email' => '<EMAIL>'
                ],
                'status' => 'active',
            ]);
        }
    }
}
