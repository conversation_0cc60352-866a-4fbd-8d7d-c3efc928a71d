<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\School;
use App\Models\Campus;
use Illuminate\Http\Request;

class SchoolController extends Controller
{
    /**
     * Get all active schools
     */
    public function index()
    {
        $schools = School::active()->orderBy('name')->get();
        return response()->json($schools);
    }

    /**
     * Get campuses for a specific school
     */
    public function getCampuses(School $school)
    {
        $campuses = $school->activeCampuses()->orderBy('name')->get();
        return response()->json($campuses);
    }
}
