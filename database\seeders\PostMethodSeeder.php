<?php

namespace Database\Seeders;

use App\Models\PostMethod;
use App\Models\Tag;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PostMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Post Methods
        $userMethod = PostMethod::create([
            'name' => 'User Posts',
            'slug' => 'user',
            'description' => 'Student-generated content - informal, personal, or expression-based posts',
            'is_active' => true,
        ]);

        $organizationMethod = PostMethod::create([
            'name' => 'Organizational Posts',
            'slug' => 'organization',
            'description' => 'Official org accounts - formal, informative, and related to transparency or events',
            'is_active' => true,
        ]);

        $groupMethod = PostMethod::create([
            'name' => 'Group Posts',
            'slug' => 'group',
            'description' => 'Clubs, committees, private threads - collaborative or group-focused',
            'is_active' => true,
        ]);

        // User Posts Tags
        $userTags = [
            ['name' => 'Personal', 'color' => '#10B981'],
            ['name' => 'Reflection', 'color' => '#8B5CF6'],
            ['name' => 'Testimonial', 'color' => '#F59E0B'],
            ['name' => 'Shout-out', 'color' => '#EF4444'],
            ['name' => 'Celebration', 'color' => '#F97316'],
            ['name' => 'Achievement', 'color' => '#84CC16'],
            ['name' => 'Study Tips', 'color' => '#06B6D4'],
            ['name' => 'Peer Request', 'color' => '#8B5CF6'],
            ['name' => 'Question', 'color' => '#3B82F6'],
            ['name' => 'Looking For', 'color' => '#6366F1'],
        ];

        foreach ($userTags as $tag) {
            Tag::create([
                'name' => $tag['name'],
                'slug' => Str::slug($tag['name']),
                'color' => $tag['color'],
                'post_method_id' => $userMethod->id,
                'is_active' => true,
            ]);
        }

        // Organization Posts Tags
        $organizationTags = [
            ['name' => 'Announcement', 'color' => '#DC2626'],
            ['name' => 'Event', 'color' => '#7C3AED'],
            ['name' => 'Financial Report', 'color' => '#059669'],
            ['name' => 'Scholarship', 'color' => '#D97706'],
            ['name' => 'Org Update', 'color' => '#0891B2'],
            ['name' => 'Recruitment', 'color' => '#C2410C'],
            ['name' => 'Meeting Minutes', 'color' => '#4338CA'],
            ['name' => 'Election', 'color' => '#BE123C'],
            ['name' => 'Membership Drive', 'color' => '#16A34A'],
            ['name' => 'Budget Allocation', 'color' => '#CA8A04'],
            ['name' => 'Official Statement', 'color' => '#9333EA'],
            ['name' => 'Policy Update', 'color' => '#0F766E'],
            ['name' => 'Seminar / Workshop', 'color' => '#B91C1C'],
        ];

        foreach ($organizationTags as $tag) {
            Tag::create([
                'name' => $tag['name'],
                'slug' => Str::slug($tag['name']),
                'color' => $tag['color'],
                'post_method_id' => $organizationMethod->id,
                'is_active' => true,
            ]);
        }

        // Group Posts Tags
        $groupTags = [
            ['name' => 'Discussion', 'color' => '#2563EB'],
            ['name' => 'Group Notice', 'color' => '#DC2626'],
            ['name' => 'Task Reminder', 'color' => '#F59E0B'],
            ['name' => 'Vote/Poll', 'color' => '#8B5CF6'],
            ['name' => 'Agenda', 'color' => '#059669'],
            ['name' => 'Progress Update', 'color' => '#0891B2'],
            ['name' => 'Feedback Request', 'color' => '#EA580C'],
            ['name' => 'Study Session', 'color' => '#7C2D12'],
            ['name' => 'Resource Sharing', 'color' => '#166534'],
            ['name' => 'Planning', 'color' => '#7C3AED'],
        ];

        foreach ($groupTags as $tag) {
            Tag::create([
                'name' => $tag['name'],
                'slug' => Str::slug($tag['name']),
                'color' => $tag['color'],
                'post_method_id' => $groupMethod->id,
                'is_active' => true,
            ]);
        }
    }
}
