<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class School extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'abbreviation',
        'description',
        'logo',
        'website',
        'address',
        'contact_info',
        'status',
    ];

    protected $casts = [
        'contact_info' => 'array',
    ];

    /**
     * Get all campuses for this school
     */
    public function campuses(): HasMany
    {
        return $this->hasMany(Campus::class);
    }

    /**
     * Get active campuses only
     */
    public function activeCampuses(): HasMany
    {
        return $this->campuses()->where('status', 'active');
    }

    /**
     * Get all users from this school
     */
    public function users(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get all organizations from this school
     */
    public function organizations(): HasMany
    {
        return $this->hasMany(Organization::class);
    }

    /**
     * Get all groups from this school
     */
    public function groups(): Has<PERSON>any
    {
        return $this->hasMany(Group::class);
    }

    /**
     * Scope to get only active schools
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
