<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->foreignId('school_id')->nullable()->after('created_by')->constrained()->onDelete('set null');
            $table->foreignId('campus_id')->nullable()->after('school_id')->constrained()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropForeign(['school_id']);
            $table->dropForeign(['campus_id']);
            $table->dropColumn(['school_id', 'campus_id']);
        });
    }
};
