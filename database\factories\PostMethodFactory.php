<?php

namespace Database\Factories;

use App\Models\PostMethod;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PostMethod>
 */
class PostMethodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $methods = [
            ['name' => 'User', 'slug' => 'user', 'description' => 'Posts created by individual users'],
            ['name' => 'Organization', 'slug' => 'organization', 'description' => 'Posts created by organizations'],
            ['name' => 'Group', 'slug' => 'group', 'description' => 'Posts created within groups'],
        ];

        $method = $this->faker->randomElement($methods);

        return [
            'name' => $method['name'],
            'slug' => $method['slug'],
            'description' => $method['description'],
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the post method is for users.
     */
    public function user(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'User',
            'slug' => 'user',
            'description' => 'Posts created by individual users',
        ]);
    }

    /**
     * Indicate that the post method is for organizations.
     */
    public function organization(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Organization',
            'slug' => 'organization',
            'description' => 'Posts created by organizations',
        ]);
    }

    /**
     * Indicate that the post method is for groups.
     */
    public function group(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Group',
            'slug' => 'group',
            'description' => 'Posts created within groups',
        ]);
    }

    /**
     * Indicate that the post method is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
