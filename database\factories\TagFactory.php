<?php

namespace Database\Factories;

use App\Models\Tag;
use App\Models\PostMethod;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tag>
 */
class TagFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->randomElement([
            'Announcement', 'Event', 'Financial Report', 'Scholarship', 
            'Org Update', 'Recruitment', 'Meeting Minutes', 'Election',
            'Discussion', 'Group Notice', 'Task Reminder', 'Vote/Poll',
            'Agenda', 'Progress Update', 'Feedback Request', 'Study Session',
            'Resource Sharing', 'Planning', 'General', 'Personal'
        ]);

        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->sentence(),
            'color' => $this->faker->hexColor(),
            'post_method_id' => PostMethod::factory(),
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the tag is for user posts.
     */
    public function forUser(): static
    {
        return $this->state(fn (array $attributes) => [
            'post_method_id' => PostMethod::factory()->user(),
        ]);
    }

    /**
     * Indicate that the tag is for organization posts.
     */
    public function forOrganization(): static
    {
        return $this->state(fn (array $attributes) => [
            'post_method_id' => PostMethod::factory()->organization(),
        ]);
    }

    /**
     * Indicate that the tag is for group posts.
     */
    public function forGroup(): static
    {
        return $this->state(fn (array $attributes) => [
            'post_method_id' => PostMethod::factory()->group(),
        ]);
    }

    /**
     * Indicate that the tag is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
