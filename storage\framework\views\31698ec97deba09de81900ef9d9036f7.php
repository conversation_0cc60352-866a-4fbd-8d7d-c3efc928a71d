<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['user', 'size' => 'sm', 'showAvatar' => true]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['user', 'size' => 'sm', 'showAvatar' => true]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    $sizeClasses = [
        'xs' => 'h-6 w-6',
        'sm' => 'h-8 w-8', 
        'md' => 'h-10 w-10',
        'lg' => 'h-12 w-12',
        'xl' => 'h-16 w-16'
    ];
    
    $textSizeClasses = [
        'xs' => 'text-xs',
        'sm' => 'text-sm',
        'md' => 'text-base',
        'lg' => 'text-lg',
        'xl' => 'text-xl'
    ];
    
    $avatarSize = $sizeClasses[$size] ?? $sizeClasses['sm'];
    $textSize = $textSizeClasses[$size] ?? $textSizeClasses['sm'];
?>

<a href="<?php echo e(route('profile.user', $user)); ?>" class="flex items-center space-x-2 hover:opacity-80 transition-opacity" <?php echo e($attributes); ?>>
    <?php if($showAvatar): ?>
        <img class="<?php echo e($avatarSize); ?> rounded-full flex-shrink-0"
             src="<?php echo e($user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
             alt="<?php echo e($user->name); ?>">
    <?php endif; ?>
    <span class="<?php echo e($textSize); ?> font-medium text-gray-900 hover:text-custom-green"><?php echo e($user->name); ?></span>
</a>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/user-link.blade.php ENDPATH**/ ?>