<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add school_id and campus_id foreign keys
            $table->foreignId('school_id')->nullable()->after('student_id')->constrained()->onDelete('set null');
            $table->foreignId('campus_id')->nullable()->after('school_id')->constrained()->onDelete('set null');

            // Keep the old campus column for now (we'll migrate data and then drop it)
            // We'll handle this in a separate data migration
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['school_id']);
            $table->dropForeign(['campus_id']);
            $table->dropColumn(['school_id', 'campus_id']);
        });
    }
};
