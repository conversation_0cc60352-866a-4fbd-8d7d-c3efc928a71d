<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Check if columns don't already exist before adding them
            if (!Schema::hasColumn('users', 'avatar')) {
                $table->string('avatar')->nullable()->after('email_verified_at');
            }
            if (!Schema::hasColumn('users', 'bio')) {
                $table->text('bio')->nullable()->after('avatar');
            }
            if (!Schema::hasColumn('users', 'phone')) {
                $table->string('phone')->nullable()->after('bio');
            }
            if (!Schema::hasColumn('users', 'student_id')) {
                $table->string('student_id')->nullable()->after('phone');
            }

            // Academic Information
            if (!Schema::hasColumn('users', 'campus')) {
                $table->string('campus')->nullable()->after('student_id');
            }
            if (!Schema::hasColumn('users', 'course_program')) {
                $table->string('course_program')->nullable()->after('campus');
            }
            if (!Schema::hasColumn('users', 'year_level')) {
                $table->string('year_level')->nullable()->after('course_program');
            }
            if (!Schema::hasColumn('users', 'enrollment_status')) {
                $table->enum('enrollment_status', ['active', 'loa', 'graduated'])->nullable()->after('year_level');
            }
            if (!Schema::hasColumn('users', 'college_department')) {
                $table->string('college_department')->nullable()->after('enrollment_status');
            }

            // Personal Information
            if (!Schema::hasColumn('users', 'contact_number')) {
                $table->string('contact_number')->nullable()->after('college_department');
            }
            if (!Schema::hasColumn('users', 'address')) {
                $table->text('address')->nullable()->after('contact_number');
            }
            if (!Schema::hasColumn('users', 'birthdate')) {
                $table->date('birthdate')->nullable()->after('address');
            }

            // Platform-Specific Info
            if (!Schema::hasColumn('users', 'skills_interests')) {
                $table->json('skills_interests')->nullable()->after('birthdate');
            }

            // Optional Additions
            if (!Schema::hasColumn('users', 'social_links')) {
                $table->json('social_links')->nullable()->after('skills_interests');
            }
            if (!Schema::hasColumn('users', 'achievements')) {
                $table->json('achievements')->nullable()->after('social_links');
            }

            // Privacy Settings
            if (!Schema::hasColumn('users', 'profile_visibility')) {
                $table->json('profile_visibility')->nullable()->after('achievements');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'avatar',
                'bio',
                'phone',
                'student_id',
                'campus',
                'course_program',
                'year_level',
                'enrollment_status',
                'college_department',
                'contact_number',
                'address',
                'birthdate',
                'skills_interests',
                'social_links',
                'achievements',
                'profile_visibility'
            ]);
        });
    }
};
