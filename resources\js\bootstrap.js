import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

/**
 * <PERSON>vel Echo for real-time broadcasting
 */
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT ?? 80,
    wssPort: import.meta.env.VITE_REVERB_PORT ?? 443,
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
});

/**
 * Handle logout to prevent broadcasting auth errors
 */
window.handleLogout = function(event) {
    // Disconnect Echo to prevent authentication errors
    if (window.Echo) {
        try {
            // Disconnect all channels first
            if (window.Echo.connector && window.Echo.connector.channels) {
                Object.keys(window.Echo.connector.channels).forEach(channelName => {
                    try {
                        window.Echo.leave(channelName);
                    } catch (e) {
                        console.log('Error leaving channel:', channelName, e);
                    }
                });
            }

            // Then disconnect Echo
            window.Echo.disconnect();
            console.log('Echo disconnected before logout');
        } catch (error) {
            console.log('Error disconnecting Echo:', error);
        }
    }

    // Clear any pending axios requests
    if (window.axios && window.axios.defaults) {
        // Cancel any pending requests if possible
        try {
            window.axios.defaults.timeout = 1; // Set very short timeout
        } catch (error) {
            console.log('Error setting axios timeout:', error);
        }
    }

    // Allow the form to submit normally
    return true;
}

/**
 * Disconnect Echo when the page is about to unload
 * This prevents authentication errors when navigating away
 */
window.addEventListener('beforeunload', function() {
    if (window.Echo) {
        try {
            window.Echo.disconnect();
        } catch (error) {
            // Silently handle errors during page unload
        }
    }
});
