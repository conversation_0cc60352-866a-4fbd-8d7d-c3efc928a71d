<?php

namespace Tests\Feature;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrganizationJoinTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_can_join_organization_and_membership_is_automatically_accepted()
    {
        // Create a user and an organization
        $user = User::factory()->create();
        $creator = User::factory()->create();
        
        $organization = Organization::factory()->create([
            'created_by' => $creator->id,
        ]);

        // Add creator as president
        $organization->members()->attach($creator->id, [
            'role' => 'president',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        // User joins the organization
        $response = $this->actingAs($user)
            ->post(route('organizations.join', $organization));

        // Assert successful redirect
        $response->assertRedirect();
        $response->assertSessionHas('success', 'You have successfully joined the organization!');

        // Assert user is now an active member
        $this->assertTrue($organization->members()->where('user_id', $user->id)->exists());
        
        $membership = $organization->members()->where('user_id', $user->id)->first();
        $this->assertEquals('active', $membership->pivot->status);
        $this->assertEquals('member', $membership->pivot->role);
        $this->assertNotNull($membership->pivot->joined_at);
    }

    /** @test */
    public function user_cannot_join_organization_twice()
    {
        // Create a user and an organization
        $user = User::factory()->create();
        $creator = User::factory()->create();
        
        $organization = Organization::factory()->create([
            'created_by' => $creator->id,
        ]);

        // Add creator as president
        $organization->members()->attach($creator->id, [
            'role' => 'president',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        // User joins the organization first time
        $organization->members()->attach($user->id, [
            'role' => 'member',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        // User tries to join again
        $response = $this->actingAs($user)
            ->post(route('organizations.join', $organization));

        // Assert error message
        $response->assertRedirect();
        $response->assertSessionHas('error', 'You are already a member of this organization.');
    }

    /** @test */
    public function organization_membership_default_status_is_active()
    {
        // Create a user and an organization
        $user = User::factory()->create();
        $creator = User::factory()->create();
        
        $organization = Organization::factory()->create([
            'created_by' => $creator->id,
        ]);

        // Manually add a member without specifying status to test default
        $organization->members()->attach($user->id, [
            'role' => 'member',
        ]);

        $membership = $organization->members()->where('user_id', $user->id)->first();
        
        // With the migration, default status should be 'active'
        $this->assertEquals('active', $membership->pivot->status);
    }
}
