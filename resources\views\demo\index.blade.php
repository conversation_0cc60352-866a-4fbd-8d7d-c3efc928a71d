<x-layouts.unilink-layout>
    <div class="space-y-8">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg text-white p-8">
            <h1 class="text-3xl font-bold mb-4">🎉 UniLink Dual-System Feature Demo</h1>
            <p class="text-lg opacity-90 mb-6">
                Welcome to the new dual-system feature! UniLink now supports both <strong>Organization Pages</strong> (like Facebook Pages) 
                and <strong>Student Groups</strong> (like Facebook Groups) for enhanced community engagement.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div class="bg-white bg-opacity-20 rounded-lg p-4">
                    <h3 class="font-bold text-lg mb-2">📄 Organization Pages</h3>
                    <p class="text-sm opacity-90">Official pages where only officers can post announcements. Students can follow to stay updated.</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-4">
                    <h3 class="font-bold text-lg mb-2">👥 Student Groups</h3>
                    <p class="text-sm opacity-90">Discussion spaces where all members can post, share files, and collaborate on projects.</p>
                </div>
            </div>
        </div>

        <!-- Quick Access Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('organizations.index') }}" class="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 transition-colors text-center">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <h3 class="font-bold text-lg">Browse Organizations</h3>
                <p class="text-sm opacity-90">Discover official organization pages</p>
            </a>
            
            <a href="{{ route('groups.index') }}" class="bg-green-600 text-white p-6 rounded-lg hover:bg-green-700 transition-colors text-center">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="font-bold text-lg">Join Student Groups</h3>
                <p class="text-sm opacity-90">Connect with peers in discussion groups</p>
            </a>
            
            @auth
                <a href="{{ route('groups.create') }}" class="bg-purple-600 text-white p-6 rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <h3 class="font-bold text-lg">Create New Group</h3>
                    <p class="text-sm opacity-90">Start your own student community</p>
                </a>
            @else
                <a href="{{ route('login') }}" class="bg-purple-600 text-white p-6 rounded-lg hover:bg-purple-700 transition-colors text-center">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <h3 class="font-bold text-lg">Login to Create</h3>
                    <p class="text-sm opacity-90">Sign in to create groups</p>
                </a>
            @endauth
        </div>

        <!-- Feature Highlights -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Organization Pages Features -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    Organization Pages
                </h2>
                <ul class="space-y-3 text-gray-700">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Official announcements from organization officers</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Follow system to stay updated</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Public visibility for all students</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Contact information and about section</span>
                    </li>
                </ul>
            </div>

            <!-- Student Groups Features -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Student Groups
                </h2>
                <ul class="space-y-3 text-gray-700">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>All members can post and discuss</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>File sharing (PDFs, images, documents)</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Public or private group options</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Post approval system for moderated groups</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Sample Data Preview -->
        @if($organizations->count() > 0 || $groups->count() > 0)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-6">Sample Data in Your System</h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Organizations -->
                    @if($organizations->count() > 0)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Organizations</h3>
                            <div class="space-y-3">
                                @foreach($organizations as $org)
                                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <span class="text-blue-600 font-bold text-sm">{{ substr($org->name, 0, 2) }}</span>
                                        </div>
                                        <div class="flex-1">
                                            <a href="{{ route('organizations.show', $org) }}" class="font-medium text-gray-900 hover:text-blue-600">
                                                {{ $org->name }}
                                            </a>
                                            <p class="text-sm text-gray-500">{{ Str::limit($org->description, 50) }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Groups -->
                    @if($groups->count() > 0)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Groups</h3>
                            <div class="space-y-3">
                                @foreach($groups as $group)
                                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <span class="text-green-600 font-bold text-sm">{{ substr($group->name, 0, 2) }}</span>
                                        </div>
                                        <div class="flex-1">
                                            <a href="{{ route('groups.show', $group) }}" class="font-medium text-gray-900 hover:text-green-600">
                                                {{ $group->name }}
                                            </a>
                                            <p class="text-sm text-gray-500">{{ Str::limit($group->description, 50) }}</p>
                                        </div>
                                        <span class="text-xs px-2 py-1 rounded-full {{ $group->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                            {{ ucfirst($group->visibility) }}
                                        </span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @endif

        <!-- Getting Started -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h2 class="text-xl font-bold text-yellow-800 mb-4">🚀 Getting Started</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div>
                    <h3 class="font-semibold text-yellow-800 mb-2">For Students:</h3>
                    <ol class="list-decimal list-inside space-y-1 text-yellow-700">
                        <li>Browse organizations to follow official pages</li>
                        <li>Join student groups that match your interests</li>
                        <li>Create your own group for study sessions or projects</li>
                        <li>Share files and collaborate with group members</li>
                    </ol>
                </div>
                <div>
                    <h3 class="font-semibold text-yellow-800 mb-2">For Organization Officers:</h3>
                    <ol class="list-decimal list-inside space-y-1 text-yellow-700">
                        <li>Switch your organization to "Page Mode"</li>
                        <li>Post official announcements and updates</li>
                        <li>Build a following of interested students</li>
                        <li>Create associated groups for member discussions</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</x-layouts.unilink-layout>
