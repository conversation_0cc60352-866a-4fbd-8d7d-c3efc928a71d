<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Post;
use App\Models\Tag;
use App\Models\PostMethod;
use App\Models\Organization;
use App\Models\Group;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PostFilterTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $postMethod;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        // Create or get existing post method
        $this->postMethod = PostMethod::firstOrCreate([
            'slug' => 'user'
        ], [
            'name' => 'User',
            'description' => 'Posts created by individual users',
            'is_active' => true
        ]);
    }

    /** @test */
    public function it_can_filter_posts_by_search_term()
    {
        // Create posts with different content
        $post1 = Post::factory()->create([
            'title' => 'Laravel Tutorial',
            'content' => 'Learn Laravel framework',
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $post2 = Post::factory()->create([
            'title' => 'PHP Basics',
            'content' => 'Introduction to PHP programming',
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?search=Laravel');

        $response->assertStatus(200)
            ->assertJson(['success' => true])
            ->assertJsonFragment(['title' => 'Laravel Tutorial']);
    }

    /** @test */
    public function it_can_filter_posts_by_post_type()
    {
        $organization = Organization::factory()->create();
        
        // Create user post
        $userPost = Post::factory()->create([
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'organization_id' => null,
            'group_id' => null,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Create organization post
        $orgPost = Post::factory()->create([
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'organization_id' => $organization->id,
            'group_id' => null,
            'status' => 'published',
            'published_at' => now()
        ]);

        // Filter for user posts only
        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?post_type=user');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        // Should contain user post but not org post
        $this->assertStringContains($userPost->title, $response->json('html'));
    }

    /** @test */
    public function it_can_filter_posts_by_tags()
    {
        $tag = Tag::firstOrCreate([
            'name' => 'Technology',
            'post_method_id' => $this->postMethod->id
        ], [
            'slug' => 'technology',
            'color' => '#3B82F6',
            'is_active' => true
        ]);

        $post = Post::factory()->create([
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $post->tags()->attach($tag->id);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?tags[]=' . $tag->id);

        $response->assertStatus(200)
            ->assertJson(['success' => true]);
    }

    /** @test */
    public function it_can_filter_posts_by_date_range()
    {
        // Create posts with different dates
        $todayPost = Post::factory()->create([
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $oldPost = Post::factory()->create([
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'status' => 'published',
            'published_at' => now()->subWeek()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?date_range=today');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        // Should contain today's post
        $this->assertStringContains($todayPost->title, $response->json('html'));
    }

    /** @test */
    public function it_returns_paginated_results()
    {
        // Create multiple posts
        Post::factory()->count(15)->create([
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter');

        $response->assertStatus(200)
            ->assertJson(['success' => true])
            ->assertJsonStructure([
                'pagination' => [
                    'current_page',
                    'last_page',
                    'total',
                    'has_more'
                ]
            ]);
    }

    /** @test */
    public function it_handles_multiple_filter_combinations()
    {
        $tag = Tag::firstOrCreate([
            'name' => 'Event',
            'post_method_id' => $this->postMethod->id
        ], [
            'slug' => 'event',
            'color' => '#10B981',
            'is_active' => true
        ]);

        $post = Post::factory()->create([
            'title' => 'Laravel Conference',
            'user_id' => $this->user->id,
            'post_method_id' => $this->postMethod->id,
            'status' => 'published',
            'published_at' => now()
        ]);

        $post->tags()->attach($tag->id);

        $response = $this->actingAs($this->user)
            ->getJson('/posts-filter?search=Laravel&tags[]=' . $tag->id . '&date_range=today');

        $response->assertStatus(200)
            ->assertJson(['success' => true]);
    }
}
