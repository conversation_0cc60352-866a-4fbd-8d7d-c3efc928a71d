<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add the new enum values to the existing enum
        DB::statement("ALTER TABLE posts MODIFY COLUMN visibility ENUM(
            'public',
            'followers_only',
            'university_only',
            'same_school_only',
            'same_campus_only',
            'private',
            'organization_only',
            'officers_only',
            'group_members_only'
        ) DEFAULT 'public'");

        // Update existing 'university_only' posts to 'same_school_only'
        DB::table('posts')
            ->where('visibility', 'university_only')
            ->update(['visibility' => 'same_school_only']);

        // Remove the old 'university_only' value from the enum
        DB::statement("ALTER TABLE posts MODIFY COLUMN visibility ENUM(
            'public',
            'followers_only',
            'same_school_only',
            'same_campus_only',
            'private',
            'organization_only',
            'officers_only',
            'group_members_only'
        ) DEFAULT 'public'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back 'university_only' to the enum
        DB::statement("ALTER TABLE posts MODIFY COLUMN visibility ENUM(
            'public',
            'followers_only',
            'university_only',
            'same_school_only',
            'same_campus_only',
            'private',
            'organization_only',
            'officers_only',
            'group_members_only'
        ) DEFAULT 'public'");

        // Revert 'same_school_only' posts back to 'university_only'
        DB::table('posts')
            ->where('visibility', 'same_school_only')
            ->update(['visibility' => 'university_only']);

        // Remove the new enum values
        DB::statement("ALTER TABLE posts MODIFY COLUMN visibility ENUM(
            'public',
            'followers_only',
            'university_only',
            'private',
            'organization_only',
            'officers_only',
            'group_members_only'
        ) DEFAULT 'public'");
    }
};
